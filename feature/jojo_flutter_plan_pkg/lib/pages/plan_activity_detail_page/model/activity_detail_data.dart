
import 'dart:ui';

import 'package:flutter/cupertino.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/utils/log.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_activity_detail_page/model/activity_data.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_home_lesson/model/course_promote_finish.dart';
import '../../activity_my_gift/gift_model.dart';

class PlanActivityDetailData {
  String? title;
  List<String> imageUrlList = [];  // 要下载动图片地址
  List<String> animationResourceList = [];  // 要下载动动效资源地址
  List<dynamic> itemList = [];  // 列表对象
  List<MyGiftModel> myGiftList = []; // 我的所有获得的奖励(我的奖励页用)
  PlanActivityDetailDataActivityVo activityVo = PlanActivityDetailDataActivityVo();  // 活动对象

  static PlanActivityDetailData? getMyGiftPageModel(PlanActivityData? data, int activityId) {
    PlanActivityDetailData model = PlanActivityDetailData();
    model.activityVo.activityId = activityId;
    // 处理已完课的节点对象
    List<ClassActivitiesTaskVo?>? tasks = data?.tasks;
    if (tasks != null && tasks.isNotEmpty) {
      _dealWithMyGiftPageTaskList(model, tasks);
    } else {
      l.e("促完课活动", "我的奖励页,任务对象为空");
      return null;
    }
    return model;
  }

  static void _dealWithMyGiftPageTaskList(PlanActivityDetailData model, List<ClassActivitiesTaskVo?> tasks) {
    List<LessonInProgressGiftModel> list = [];
    for (var element in tasks) {
      LessonInProgressGiftModel? giftModel = _getGiftModel(model, element, tasks.length, tasks.indexOf(element));
      if (giftModel != null && giftModel.isGet == true) {
        giftModel.nodeIndex = tasks.indexOf(element);
        list.add(giftModel);
      }
    }
    // 先按照完成时间进行倒序排序
    list.sort((a, b) => b.finishTime.compareTo(a.finishTime));
    // 处理排序之后的奖励
    List<MyGiftModel> allGifts = [];
    for (var item in list) {
      List<MyGiftModel> myGiftList = item.rewardList
          .map((element) => _createMyGiftModel(element, item.giftSpineUrl ?? "", item.nodeIndex))
          .toList();
      // 对当前阶段的奖励进行排序
      _sortMyGiftList(myGiftList);
      allGifts.addAll(myGiftList);
    }

    model.myGiftList = allGifts;
  }

  static void _sortMyGiftList(List<MyGiftModel> myGiftList) {
    myGiftList.sort((a, b) {
      if (a.getTime == 0 && b.getTime == 0) {
        return 0;
      } else if (a.getTime == 0) {
        return -1;
      } else if (b.getTime == 0) {
        return 1;
      } else {
        return b.getTime.compareTo(a.getTime);
      }
    });
  }

  static MyGiftModel _createMyGiftModel(LessonInProgressGiftRewardModel element, String rewardUrl, int index) {
    MyGiftModel model = MyGiftModel(
      isNew: false,
      index: index,
      title: element.title,
      subTitle: element.subTitle,
      spineAnimationName: element.animationName,
      route: element.route,
      isGet: element.isGet,
      type: element.type,
      medalId: element.medalId,
      getTime: element.getTime,
    );
    model.spineResourceVo = PromoteLessonFinishSpineResourceVo();
    model.spineResourceVo?.resourceUrl = rewardUrl;
    return model;
  }

  /// 根据请求到的数据转化成自己需要的对象
  static PlanActivityDetailData? getDataModel(PlanActivityData? data, int activityId) {
    PlanActivityDetailData model = PlanActivityDetailData();
    model.activityVo.activityId = activityId;
    // 进度条颜色以及背景动效资源
    ClassActivitiesThemeCardVo? themeCard = data?.themeCard;
    if (themeCard != null) {
      _dealWithBgResource(model, themeCard);
    } else {
      l.e("促完课活动", "详情页背景资源对象为空");
      return null;
    }

    // 处理资源内容
    List<PlanActivityFreePagesVo?> freePages = data?.freePages ?? [];
    if (freePages.isNotEmpty) {
      PlanActivityFreePagesVo? firstPage = freePages.first;
      model.title = firstPage?.pageName;
      _dealWithResource(model, firstPage);
    } else {
      l.e("促完课活动", "详情页资源对象为空");
      return null;
    }
    // 处理任务对象
    List<ClassActivitiesTaskVo?>? tasks = data?.tasks;
    if (tasks != null && tasks.isNotEmpty) {
      _dealWithTaskList(model, tasks);
    } else {
      l.e("促完课活动", "详情页任务对象为空");
      return null;
    }
    return model;
  }

  static void _dealWithBgResource(PlanActivityDetailData model, ClassActivitiesThemeCardVo themeCard) {
    model.activityVo.progressStartColor = themeCard.progressHeadColor;
    model.activityVo.progressEndColor = themeCard.progressTailColor;
    model.activityVo.promoteFinishModel = PromoteLessonFinishModel();
    model.activityVo.promoteFinishModel?.activityId = model.activityVo.activityId;
    model.activityVo.promoteFinishModel?.lessonInProgress = LessonInProgressModel();
    model.activityVo.promoteFinishModel?.lessonInProgress?.isMultistage = true;
    model.activityVo.promoteFinishModel?.lessonInProgress?.spineResourceInfo = PromoteLessonFinishSpineResourceInfo();
    model.activityVo.promoteFinishModel?.lessonInProgress?.spineResourceInfo?.badgeBgResource.resourceUrl = themeCard.backgroundRes;
    if (themeCard.backgroundRes != null) {
      model.animationResourceList.add(themeCard.backgroundRes ?? "");
    }
  }

  // 处理资源内容
  static void _dealWithResource(PlanActivityDetailData model, PlanActivityFreePagesVo? pagesVo) {
    int index = 0;
    for (var element in pagesVo?.components ?? []) {
      if (element.componentType == FreePagesActivityPopupConstants.img) {
        // 图片资源
        PlanActivityDetailDataPicVo picVo = PlanActivityDetailDataPicVo();
        picVo.index = index;
        picVo.url = element.imgUrl;
        model.itemList.add(picVo);
      } else if (element.componentType == FreePagesActivityPopupConstants.video) {
        // 视频资源
        PlanActivityDetailDataVideoVo videoVo = PlanActivityDetailDataVideoVo();
        videoVo.index = index;
        videoVo.picVo.url = element.videoBgImg;
        videoVo.videoUrl = element.videoUrl;
        videoVo.coverUrl = element.videoCoverImg;
        model.itemList.add(videoVo);
      } else if (element.componentType == FreePagesActivityPopupConstants.taskProgress) {
        // 任务进度
        if (element.topImg != null && !model.imageUrlList.contains(element.topImg)) {
          model.imageUrlList.add(element.topImg);
        }
        if (element.surroundImg != null && !model.imageUrlList.contains(element.surroundImg)) {
          model.imageUrlList.add(element.surroundImg);
        }
        if (element.bottomImg != null && !model.imageUrlList.contains(element.bottomImg)) {
          model.imageUrlList.add(element.bottomImg);
        }
        model.activityVo.index = index;
        model.activityVo.topPicVo.url = element.topImg;
        model.activityVo.middlePicVo.url = element.surroundImg;
        model.activityVo.bottomPicVo.url = element.bottomImg;
        model.itemList.add(model.activityVo);
      }
      index++;
    }
  }

  static void _dealWithTaskList(PlanActivityDetailData model, List<ClassActivitiesTaskVo?> tasks) {
    for (var element in tasks) {
      LessonInProgressGiftModel? giftModel = _getGiftModel(model, element, tasks.length, tasks.indexOf(element));
      if (giftModel != null) {
        model.activityVo.giftList.add(giftModel);
      }
    }
  }

  static LessonInProgressGiftModel? _getGiftModel(PlanActivityDetailData model, ClassActivitiesTaskVo? taskVo, int length, int index) {
    ClassActivitiesTaskConditionsVo? conditionsVo = taskVo?.conditions?.first;
    ClassActivitiesTaskRewardsVo? taskRewardsVo = taskVo?.rewards?.first;
    if (conditionsVo == null || taskRewardsVo == null) return null;

    LessonInProgressGiftModel item = LessonInProgressGiftModel();
    item.currentValue = conditionsVo.currentValue ?? 0;
    item.index = conditionsVo.targetValue ?? 0;
    item.activityId = model.activityVo.activityId;
    item.medalId = taskRewardsVo.rewardId;
    item.taskId = taskVo?.taskId;
    item.isGet = item.currentValue >= item.index;
    item.type = taskRewardsVo.type;
    item.finishTime = taskVo?.finishTime ?? 0;
    int rewardsLength = taskVo?.rewards?.length ?? 0;
    bool isMultistageReward = rewardsLength > 1;
    taskVo?.rewards?.forEach((element) {
      LessonInProgressGiftRewardModel rewardItem = LessonInProgressGiftRewardModel();
      rewardItem.activityId = model.activityVo.activityId;
      rewardItem.taskId = taskVo.taskId;
      rewardItem.medalId = element?.rewardId;
      rewardItem.type = element?.type;
      _dealWithRouteOfRewardItem(rewardItem, element?.rewardBizUrl);
      rewardItem.title = getGiftRewardNodeModel(taskVo.taskExtendResource?.rewardNodeTexts, rewardItem.type)?.mainText ?? "";
      rewardItem.subTitle = getGiftRewardNodeModel(taskVo.taskExtendResource?.rewardNodeTexts, rewardItem.type)?.subText ?? "";
      rewardItem.isGet = element?.isGet == PromoteLessonMedalGitStatus.GIT;
      rewardItem.getTime = element?.getTime ?? 0;
      if (isMultistageReward) {
        // 多奖励,使用后端返回的类型->动画名称以及语音名称
        rewardItem.animationName = PromoteLessonFinishModel.getGiftAnimationName(rewardItem.type);
        rewardItem.audioSoundEffectsName = "${AudioResourceConstants.audioEventSoundEffectsPre}${rewardItem.animationName ?? ""}.mp3";
        rewardItem.audioDubbingName = "${AudioResourceConstants.audioEventDubbingPre}${rewardItem.animationName ?? ""}.mp3";
      } else {
        // 单奖励,使用单奖励的动效以及语音规范
        rewardItem.animationName = SpineAnimationConstants.badgeInfoAnimationFull;
        rewardItem.audioSoundEffectsName = AudioResourceConstants.audioEventSoundEffectsOldFull;
        rewardItem.audioDubbingName = AudioResourceConstants.audioEventDubbingFull;
      }
      item.rewardList.add(rewardItem);
    });
    item.giftSpineUrl = taskVo?.taskExtendResource?.rewardDisplayUrl;
    // 对于勋章累的奖励，动效资源为空时需要从另一处配置中获取
    if (item.type == PromoteLessonRewardsType.normal && (item.giftSpineUrl ?? "").isEmpty) {
      item.giftSpineUrl = taskRewardsVo.resourceFlutter;
    }
    String url = item.giftSpineUrl ?? "";
    item.spineResourceVo = PromoteLessonFinishSpineResourceVo();
    if (url.isNotEmpty) {
      model.animationResourceList.add(url);
    } else {
      item.spineResourceVo?.isDowned = true;
    }
    item.spineResourceVo?.resourceUrl = item.giftSpineUrl;
    return item;
  }

  static ClassActivitiesTaskRewardNodeTextVo? getGiftRewardNodeModel(List<ClassActivitiesTaskRewardNodeTextVo?>? rewardNodeTexts, int? type) {
    for (var item in rewardNodeTexts ?? []) {
      if (item?.rewardType == type) {
        return item;
      }
    }
    return null;
  }

  static void _dealWithRouteOfRewardItem(LessonInProgressGiftRewardModel model, String? rewardBizUrl) {
    model.route = rewardBizUrl;
    if (model.type == PromoteLessonRewardsType.dress) {
      model.route = "tinman-router://cn.tinman.jojoread/flutter/space/mydress?windowType=window";
    }
    if (model.type == PromoteLessonRewardsType.normal) {
      model.route = "tinman-router://cn.tinman.jojoread/flutter/plan/shareAchievement?windowType=window&medalId=${model.medalId}";
    }
  }
}

class PlanActivityDetailDataActivityVo {
  int index = 0;  // 列表中的位置
  int? activityId; // 活动Id
  String? progressStartColor;  // 进度条渐变开始颜色
  String? progressEndColor;  // 进度条渐变结束颜色
  PromoteLessonFinishModel? promoteFinishModel;  // 促完课对象（点击节点后展示弹窗有用）
  List<LessonInProgressGiftModel> giftList = [];  // 阶段奖励数组
  PlanActivityDetailDataPicVo topPicVo = PlanActivityDetailDataPicVo();  // 顶部图片对象
  PlanActivityDetailDataPicVo middlePicVo = PlanActivityDetailDataPicVo();  // 中间图片对象
  PlanActivityDetailDataPicVo bottomPicVo = PlanActivityDetailDataPicVo();  // 底部图片对象
}

class PlanActivityDetailDataPicVo {
  int index = 0;  // 列表中的位置
  double width = 0;
  double height = 0;
  String? url;  // 网络地址
  String? localPath;   // 本地地址
}

class PlanActivityDetailProgressItemVo {
  Offset? startPoint;
  Offset? endPoint;
  Offset? controlPoint;
}

class PlanActivityDetailProgressVo {
  double progress = 0.0;
  List<PlanActivityDetailProgressItemVo> itemList = [];
}

class PlanActivityDetailDataVideoVo {
  int index = 0; // 列表中的位置
  PlanActivityDetailDataPicVo picVo = PlanActivityDetailDataPicVo();  // 背景图片对象
  String? videoUrl;  // 视频地址
  String? coverUrl;  // 封面地址
}

class FreePagesActivityPopupConstants {
  static const String img = "IMG";
  static const String video = "VIDEO";
  static const String taskProgress = "TASK_PROGRESS";
}

class ActivityDetailSizeConstants {
  static double get leftNodeLeftSpace => 53.rdp;  // 最左侧的节点距离左侧屏幕的距离
  static double get rightNodeRightSpace => 47.rdp;  // 最右侧的节点距离右侧屏幕的距离
  static double get rightNodeCenterToRightSpace => 86.rdp;  // 最左侧的节点中心距离右侧屏幕的距离
  static double get startNodeLeftSpace => 45.rdp; // 开始图标距离左侧屏幕的距离
  static double get nodeWidth => 70.rdp;  // 节点宽度
  static double get nodeHeight => 114.rdp;  // 节点高度
  static double get nodeFinishRightSpace => 10.rdp;  // 完课勾勾距离右侧的距离
  static double get taskContentTopSpace => 98.rdp;  // 任务组件距离顶部的距离
  static double get rowProgressCenterSpace => 135.rdp;  // 两行进度条之间的距离
  static double get progressLrSpace => 30.rdp;  // 进度条左右边缘距离屏幕边缘的距离
  static double get flagSpace => 15.rdp;  // 最后小旗延伸出去的宽度
  static double get pregressToFlagSpace => 38.rdp;  // 进度条到小旗到距离
  static double get nodeToControlSpace => 56.rdp;  // 节点到贝塞尔控制点的距离
}

/// 落地页生命周期通知
class EventBusActivityDetailLifecycleData {
  final LifecycleType lifecycleType;
  EventBusActivityDetailLifecycleData(this.lifecycleType);
}