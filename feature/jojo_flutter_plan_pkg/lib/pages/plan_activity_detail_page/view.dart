
import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/utils/log.dart';
import 'package:jojo_flutter_base/widgets/app_bars/appbar_left.dart';
import 'package:jojo_flutter_base/widgets/app_bars/jojo_appbar.dart';
import 'package:jojo_flutter_base/widgets/common/page_loading_v25.dart';
import 'package:jojo_flutter_plan_pkg/generated/l10n.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_activity_detail_page/controller.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_activity_detail_page/model/activity_detail_data.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_activity_detail_page/state.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_activity_detail_page/utils/image_height_calculator.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_activity_detail_page/widget/activity_image_widget.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_activity_detail_page/widget/activity_task_widget.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_activity_detail_page/widget/activity_video_widget.dart';

import '../../common/host_env/host_env.dart';

class PlanActivityDetailPageView extends StatefulWidget {

  final PlanActivityDetailState state;

  const PlanActivityDetailPageView({super.key, required this.state,});

  @override
  State<StatefulWidget> createState() {
    return PlanActivityDetailPageViewState();
  }
}

class PlanActivityDetailPageViewState extends State<PlanActivityDetailPageView>{

  final ScrollController _scrollController = ScrollController();
  double _totalHeightBeforeActivity = 0.0; // 存储 ActivityVo 之前元素的高度总值

  @override
  void didUpdateWidget(covariant PlanActivityDetailPageView oldWidget) {
    super.didUpdateWidget(oldWidget);
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _calculateHeightBeforeActivity();
    });
  }

  void _calculateHeightBeforeActivity() {
    final itemList = widget.state.detailData?.itemList ?? [];
    if (itemList.isEmpty) return;

    final screenWidth = MediaQuery.of(context).size.width;

    ImageHeightCalculator.calculateHeightBeforeActivity(
      itemList: itemList,
      screenWidth: screenWidth,
      onComplete: (totalHeight) {
        _totalHeightBeforeActivity = totalHeight;
        _handleAllImagesLoadedComplete();
      },
      onProgress: (completed, total, currentHeight) {},
    );
  }

  /// 处理所有图片加载完成后的逻辑
  void _handleAllImagesLoadedComplete() {
    if (mounted) {
      final screenHeight = MediaQuery.of(context).size.height;
      if (_totalHeightBeforeActivity > screenHeight * 0.8) {
        _scrollController.animateTo(_totalHeightBeforeActivity, duration: const Duration(milliseconds: 300), curve: Curves.ease);
      }
      setState(() {});
    }
  }

  @override
  Widget build(BuildContext context) {
    return JoJoPageLoadingV25(
        scene: PageScene.common,
        hideProgress: true,
        exception: widget.state.exception,
        retry: () {
          if (!mounted) return;
          try {
            context.read<PlanActivityDetailCtrl>().refreshPage();
          } catch (e) {
            l.i("促完课活动", "落地页，重试按钮点击异常：${e.toString()}");
          }
        },
        backWidget: Positioned(
            top: MediaQuery.of(context).padding.top,
            child: const AppbarLeft()),
        status: widget.state.pageStatus,
        child: Scaffold(
            primary: !JoJoRouter.isWindow,
            appBar: JoJoAppBar(
              title: widget.state.detailData?.title ?? "",
              backgroundColor: Colors.transparent,
              centerTitle: true,
              actions: [
                GestureDetector(
                  behavior: HitTestBehavior.translucent,
                  onTap: () {
                    if (!mounted) return;
                    try {
                      PlanActivityDetailCtrl _ctrl = context.read<PlanActivityDetailCtrl>();
                      // 浏览埋点
                      String? buriedDataString;
                      if (_ctrl.buriedString != null) {
                        Map<String, dynamic> buriedMap = jsonDecode(_ctrl.buriedString!);
                        Map<String, dynamic> properties = {
                          '\$element_name': "完课活动_活动页_点击查看我的奖励",
                          ...buriedMap,
                        };
                        RunEnv.sensorsTrack("\$AppClick", properties);
                        buriedDataString = Uri.encodeComponent(jsonEncode(buriedMap));
                      }
                      _ctrl.jumpToMyGiftPage(buriedDataString);
                    } catch (e) {
                      l.i("促完课活动", "落地页，视频组件点击埋点异常");
                    }
                  },
                  child: Container(
                    alignment: Alignment.center,
                    height: 44.rdp,
                    padding: EdgeInsets.only(right: 10.rdp),
                    child: Text(
                      S.of(context).myGift,
                      style: TextStyle(
                        fontSize: 18.rdp,
                        color: context.appColors.jColorGray5,
                        fontWeight: FontWeight.w400,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ),
                )
              ],),
            body: ListView.builder(
                itemCount: (widget.state.detailData?.itemList ?? []).length,
                controller: _scrollController,
                padding: EdgeInsets.zero,
                physics: const ClampingScrollPhysics(),
                itemBuilder: (context, index) {
                  double screenWidth = MediaQuery.of(context).size.width;
                  List<dynamic> itemList = widget.state.detailData?.itemList ?? [];
                  var item = itemList[index];
                  if (item is PlanActivityDetailDataPicVo) {
                    return ActivityImageWidget(picVo: item,);
                  }
                  if (item is PlanActivityDetailDataVideoVo) {
                    return ActivityVideoWidget(videoVo: item,);
                  }
                  if (item is PlanActivityDetailDataActivityVo) {
                    return ActivityTaskWidget(activityVo: item, screenWidth: screenWidth,);
                  }
                  return Container();
                })
        ));
  }
}