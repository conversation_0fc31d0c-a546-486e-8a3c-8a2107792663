
import 'package:flutter/material.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/utils/hex_color.dart';
import 'package:jojo_flutter_base/widgets/app_bars/appbar_left.dart';
import 'package:jojo_flutter_base/widgets/app_bars/jojo_appbar.dart';
import 'package:jojo_flutter_base/widgets/common/page_loading_v25.dart';
import 'package:jojo_flutter_plan_pkg/generated/l10n.dart';
import 'package:jojo_flutter_plan_pkg/pages/activity_my_gift/state.dart';

import 'controller.dart';
import 'empty_view.dart';
import 'gift_model.dart';
import 'gift_view.dart';

class ActivityMyGiftPageView extends StatefulWidget {

  final ActivityMyGiftState state;

  const ActivityMyGiftPageView({super.key, required this.state});

  @override
  State<StatefulWidget> createState() {
    return ActivityMyGiftPageViewState();
  }
}

class ActivityMyGiftPageViewState extends State<ActivityMyGiftPageView>{

  final ScrollController _scrollController = ScrollController();

  @override
  Widget build(BuildContext context) {
    ActivityMyGiftCtrl ctrl = context.read<ActivityMyGiftCtrl>();
    return JoJoPageLoadingV25(
        scene: PageScene.common,
        hideProgress: true,
        exception: widget.state.exception,
        retry: () {
          if (!mounted) return;
          ActivityMyGiftCtrl _ctrl = context.read<ActivityMyGiftCtrl>();
          _ctrl.showLoadingPage();
          _ctrl.getDetailInfoData();
        },
        backWidget: Positioned(
            top: MediaQuery.of(context).padding.top,
            child: const AppbarLeft()),
        status: widget.state.pageStatus,
        child: Scaffold(
            primary: !JoJoRouter.isWindow,
            appBar: JoJoAppBar(
              title: S.of(context).myGift,
              backgroundColor: Colors.transparent,
              centerTitle: true,),
            body: _buildContentWidget(ctrl)
        ));
  }

  Widget _buildContentWidget(ActivityMyGiftCtrl ctrl) {
    bool hasGift = ctrl.myGiftList.isNotEmpty;
    return hasGift ? Container(
      color: context.appColors
          .colorVariant2(HexColor(ctrl.subjectColor ?? "#FFEBDE")),
      padding: EdgeInsets.only(left: 20.rdp, right: 20.rdp),
      child: _buildListWidget(ctrl),
    ) : Container(
      color: Colors.white,
      child: const ActivityMyGiftEmptyWidget(),
    );
  }

  Widget _buildListWidget(ActivityMyGiftCtrl ctrl) {
    int itemCount = (ctrl.myGiftList.length + 1) ~/ 2;
    return ListView.builder(
        itemCount: itemCount + 1,
        controller: _scrollController,
        padding: EdgeInsets.zero,
        physics: const ClampingScrollPhysics(),
        itemBuilder: (context, index) {
          if (index == 0) {
            return SizedBox(height: 20.rdp,);
          }
          int leftIndex = (index - 1) * 2;
          int rightIndex = (index - 1) * 2 + 1;
          MyGiftModel leftGift = ctrl.myGiftList[leftIndex];
          MyGiftModel? rightGift;
          if (rightIndex <= ctrl.myGiftList.length - 1) {
            rightGift = ctrl.myGiftList[rightIndex];
          }
          return SizedBox(
            height: 230.rdp,
            child: Column(
              children: [
                Container(
                  color: Colors.transparent,
                  height: 218.rdp,
                  child: Row(
                    children: [
                      Expanded(flex: 1, child: ActivityMyGiftWidget(myGiftModel: leftGift,)),
                      SizedBox(width: 10.rdp,),
                      Expanded(flex: 1, child: rightGift != null ? ActivityMyGiftWidget(myGiftModel: rightGift,) : Container()),
                    ],
                  ),
                ),
                SizedBox(height: 12.rdp,),
              ],
            ),
          );
        });
  }
}
