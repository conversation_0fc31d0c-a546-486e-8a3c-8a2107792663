

import 'dart:convert';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/spine/jojo_spine_animation_control.dart';
import 'package:jojo_flutter_base/spine/jojo_spine_animation_widget.dart';
import 'package:jojo_flutter_base/utils/hex_color.dart';
import 'package:jojo_flutter_base/utils/log.dart';
import 'package:jojo_flutter_base/widgets/common/cached_network_image_pro.dart';
import 'package:jojo_flutter_base/widgets/common/image_asset_pro.dart';

import '../../common/config/config.dart';
import '../../common/host_env/host_env.dart';
import '../../static/img.dart';
import '../plan_home_lesson/model/course_promote_finish.dart';
import '../plan_home_lesson/utils/promote_finish_buried_utils.dart';
import 'controller.dart';
import 'gift_model.dart';

class ActivityMyGiftWidget extends StatefulWidget {

  final MyGiftModel myGiftModel;

  const ActivityMyGiftWidget({
    super.key,
    required this.myGiftModel,
  });

  @override
  State<StatefulWidget> createState() {
    return _ActivityMyGiftWidgetState();
  }
}

class _ActivityMyGiftWidgetState extends State<ActivityMyGiftWidget> {

  final _spineController = JoJoSpineAnimationController();
  bool _needStopAnimation = false;

  void _playAnimation() {
    String animationName = SpineAnimationConstants.bubbleAnimationLoop;
    if (_spineController.spineController?.skeletonData.findAnimation(animationName) == null) {
      animationName = SpineAnimationConstants.badgeInfoAnimationNormal;
    }
    _spineController.playAnimation(JoJoSpineAnimation(
        animaitonName: animationName,
        trackIndex: 0,
        loop: false,
        delay: 0, listener: _spineAnimationEvent));
  }

  /// 动效动画完成回调
  _spineAnimationEvent(AnimationEventType type) {
    if(AnimationEventType.start == type && _needStopAnimation) {
      _spineController.pauseAnimation();
      _needStopAnimation = false;
    }
  }

  Widget _buildSpineWidget() {
    // 先查看是否有封面资源,如果有,则取封面展示,没有再用spine资源兜底
    String imageName = "${widget.myGiftModel.spineAnimationName ?? ""}.png";
    String imageFilePath = widget.myGiftModel.spineResourceVo?.getFilePath(imageName) ?? "";
    if (imageFilePath.isNotEmpty) {
      final file = File(imageFilePath);
      return Image.file(
        file,
        width: 80.rdp,
        height: 80.rdp,
        fit: BoxFit.cover,
      );
    } else {
      String? atlasFilePath = widget.myGiftModel.spineResourceVo?.getFilePath(SpineResourceConstants.SPINE_ATS);
      String? skelFilePath = widget.myGiftModel.spineResourceVo?.getFilePath(SpineResourceConstants.SPINE_SKL);
      if ((atlasFilePath ?? "").isEmpty || (skelFilePath ?? "").isEmpty) {
        return ImageNetworkCached(
          imageUrl: '',
          width: 80.rdp,
          height: 80.rdp,
          fit: BoxFit.cover,
        );
      }

      Key spineKey = ValueKey('$atlasFilePath-$skelFilePath');
      return SizedBox(
        key: spineKey,
        width: 80.rdp,
        height: 80.rdp,
        child: _buildAnimationWidget(atlasFilePath, skelFilePath),
      );
    }
  }

  Widget _buildAnimationWidget(String? atlasFilePath, String? skelFilePath) {
    return JoJoSpineAnimationWidget(
      atlasFilePath ?? "",
      skelFilePath ?? "",
      LoadMode.file,
      _spineController,
      useRootBoneAlign: true,
      fit: BoxFit.none,
      onInitialized: (controller) {
        if (mounted) {
          controller.skeleton
            ..setScaleX(80.rdp/500)
            ..setScaleY(80.rdp/500);
          _playAnimation();
          _needStopAnimation = true;
        }
      },
    );
  }

  Widget _buildGiftWidget() {
    String btnName = PromoteLessonFinishModel.getGiftBtnName(context, widget.myGiftModel.type, widget.myGiftModel.isGet);
    return Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        SizedBox(height: 14.rdp,),
        _buildSpineWidget(),
        SizedBox(height: 8.rdp,),
        SizedBox(
          height: 27.rdp,
          child: Center(
            child: Text(
              widget.myGiftModel.title ?? "",
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
              style: TextStyle(
                  color: context.appColors.jColorGray5,
                  fontSize: 18.rdp,
                  fontWeight: RunEnv.isAndroid ? FontWeight.w500 : FontWeight.w600),
            ),
          ),
        ),
        SizedBox(height: 4.rdp,),
        SizedBox(
          height: 21.rdp,
          child: Center(
            child: Text(
              widget.myGiftModel.subTitle ?? "",
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
              style: TextStyle(
                  color: context.appColors.jColorGray4,
                  fontSize: 14.rdp,
                  fontWeight: FontWeight.w400),
            ),
          ),
        ),
        Expanded(child: Container()),
        Container(
          width: double.infinity,
          alignment: Alignment.center,
          child: GestureDetector(
            onTap: () {
              if (!mounted) return;
              try {
                ActivityMyGiftCtrl _ctrl = context.read<ActivityMyGiftCtrl>();
                // 浏览埋点
                if (_ctrl.buriedString != null) {
                  Map<String, dynamic> buriedMap = jsonDecode(_ctrl.buriedString!);
                  String businessType = JoJoPromoteFinishBuriedUtils.getBusinessType(widget.myGiftModel.type);
                  Map<String, dynamic> properties = {
                    '\$element_name': "完课活动_活动页_$btnName",
                    'user_state': widget.myGiftModel.isGet ? "1" : "0",
                    'business_type' : businessType,
                    ...buriedMap,
                  };
                  RunEnv.sensorsTrack("\$AppClick", properties);
                }
              } catch (e) {
                l.i("促完课活动", "我的奖励页，奖励点击埋点异常");
              }
              String route = widget.myGiftModel.route ?? "";
              if (route.isNotEmpty) {
                RunEnv.jumpLink(route);
              }
            },
            child: Visibility(
              visible: btnName.isNotEmpty,
              child: Container(
                decoration: BoxDecoration(
                    color: HexColor('#FCDA00'),
                    borderRadius: BorderRadius.circular(15.rdp)),
                width: 80.rdp,
                height: 30.rdp,
                alignment: Alignment.center,
                child: Center(
                  child: Text(
                    btnName,
                    style: TextStyle(
                      fontSize: 14.rdp,
                      color: HexColor('#6B430B'),
                      fontWeight: RunEnv.isAndroid ? FontWeight.w500 : FontWeight.w600,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ),
              ),
            ),
          ),
        ),
        SizedBox(height: 20.rdp,),
      ],
    );
  }

  Widget _buildNewImage() {
    return Visibility(
      visible: widget.myGiftModel.isNew,
      child: ImageAssetWeb(
        assetName :AssetsImg.PLAN_IMAGE_PLAN_ACTIVITY_GIFT_NEW,
        width: 38.rdp,
        height: 20.rdp,
        fit: BoxFit.cover,
        package: Config.package,
      ),
    );
  }

  Widget _buildContentWidget() {
    return Stack(
      children: [
        Positioned.fill(
          child: _buildGiftWidget()
        ),
        Positioned(
            top: -1.rdp,
            right: -1.rdp,
            child: _buildNewImage()
        )
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    ActivityMyGiftCtrl ctrl = context.read<ActivityMyGiftCtrl>();
    return Container(
      height: 218.rdp,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: const Color(0xFFFFEBDE).withOpacity(0.5),
            spreadRadius: 2,
            blurRadius: 5,
            offset: const Offset(0, 2), // 阴影偏移量
          ),
        ],
        border: Border.all(
          color: context.appColors
              .colorVariant2(HexColor(ctrl.subjectColor ?? "#FFEBDE")),
          width: 1,
        ),
      ),
      child: _buildContentWidget(),
    );
  }
}
