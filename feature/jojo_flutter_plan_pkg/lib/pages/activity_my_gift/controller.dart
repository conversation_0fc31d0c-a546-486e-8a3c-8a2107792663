
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/config/config.dart';
import 'package:jojo_flutter_base/utils/log.dart';
import 'package:jojo_flutter_base/widgets/common/page_loading_v25.dart';
import 'package:jojo_flutter_plan_pkg/pages/activity_my_gift/state.dart';

import '../../service/plan_activity_detail_api.dart';
import '../plan_activity_detail_page/model/activity_data.dart';
import '../plan_activity_detail_page/model/activity_detail_data.dart';
import '../plan_home_lesson/utils/spine_download_manager.dart';
import 'gift_model.dart';

class ActivityMyGiftCtrl extends Cubit<ActivityMyGiftState> {
  final String? buriedString;
  final String? subjectColor;
  final int? activityId;
  final int? classId;
  final int? courseId;
  final int? pageId;
  ActivityDetailApi? pageApi;
  List<MyGiftModel> myGiftList = [];
  bool requestSuccess = false;

  ActivityMyGiftCtrl({
    ActivityDetailApi? api,
    required this.activityId,
    required this.buriedString,
    required this.subjectColor,
    required this.classId,
    required this.courseId,
    required this.pageId}) : super(ActivityMyGiftState(
      pageStatus: PageStatus.loading,
      exception: null),) {
    pageApi = api ?? ActivityDetailApis;
  }

  // 请求数据
  Future<PlanActivityData?> requestData() async {
    if ((activityId ?? 0) == 0 || (classId ?? 0) == 0 || (courseId ?? 0) == 0) {
      l.e("促完课活动", "我的奖励页，关键参数为空：$activityId $classId $courseId");
      return null;
    }
    return await pageApi?.getCourseSegmentsInfo(activityId: activityId ?? 0, classId: classId ?? 0, courseId: courseId ?? 0, pageId: pageId ?? 0);
  }

  Future<void> getDetailInfoData() async {
    try {
      PlanActivityData? data = await requestData();
      PlanActivityDetailData? detailData = PlanActivityDetailData.getMyGiftPageModel(data, activityId ?? 0);
      if (requestSuccess) {
        myGiftList = detailData?.myGiftList ?? [];
        showSuccessPage();
      } else {
        downloadResource(data, detailData);
      }
    } catch (e) {
      if (!requestSuccess) {
        // 请求成功之后再请求,默认静默刷新
        showErrorPage(Exception("接口请求失败"), "我的奖励页请求失败");
      }
    }
  }

  /// 下载资源
  void downloadResource(PlanActivityData? data,PlanActivityDetailData? detailData) {
    if (detailData == null) {
      showErrorPage(Exception("数据组装失败"), "我的奖励页，数据组装失败");
      return;
    }
    if (data == null) {
      showErrorPage(Exception("请求失败"), "我的奖励页，数据返回为空");
      return;
    }

    // 下载已完课的spine资源
    List<String> animationResourceList = [];
    for (var item in detailData.myGiftList) {
      String resourceUrl = item.spineResourceVo?.resourceUrl ?? "";
      if (resourceUrl.isNotEmpty && !animationResourceList.contains(resourceUrl)) {
        animationResourceList.add(resourceUrl);
      }
    }

    if (animationResourceList.isNotEmpty) {
      _downloadRes(animationResourceList, successListener: (Map<String, String> map) {
        dealWithAnimationData(map, detailData, false);
      }, failedListener: () {
        showErrorPage(Exception("请求失败"), "我的奖励页，动效资源下载失败");
      });
    } else {
      // 显示暂无奖励
      showSuccessPage();
    }
  }

  void showErrorPage(Exception exception, String? logString)  {
    l.e("促完课活动", logString ?? "");
    ActivityMyGiftState newState = state.copyWith();
    newState.pageStatus = PageStatus.error;
    newState.exception = exception;
    emit(newState);
  }

  void showSuccessPage() {
    ActivityMyGiftState newState = state.copyWith();
    newState.pageStatus = PageStatus.success;
    emit(newState);
  }

  void showLoadingPage() {
    ActivityMyGiftState newState = state.copyWith();
    newState.pageStatus = PageStatus.loading;
    emit(newState);
  }

  void dealWithAnimationData(Map<String, String> map, PlanActivityDetailData data, bool notNeedCheck) {
    for (var element in data.myGiftList) {
      if (map.keys.contains(element.spineResourceVo?.resourceUrl)) {
        element.spineResourceVo?.localPath = map[element.spineResourceVo?.resourceUrl];
        element.spineResourceVo?.isDowned = true;
      }
    }

    // 检查是否所有资源都下载完成
    if (isEssentialAnimationResourcesDownloaded(data, false) || notNeedCheck) {
      onAllResourcesDownloaded(data);
    }
  }

  /// 所有资源下载完成后的回调
  Future<void> onAllResourcesDownloaded(PlanActivityDetailData data) async {
    myGiftList = data.myGiftList;
    requestSuccess = true;
    MyGiftModel firstElement = myGiftList.first;
    for (MyGiftModel element in myGiftList) {
      // 获取奖励的new状态
      await getNewStatus(element, firstElement.index);
      // 处理获得时间
      setGetTime(element);
    }
    setNewStatus(firstElement.index);
    showSuccessPage();
  }

  // 下载资源
  Future<void> _downloadRes(List<String> urlList,
      {Function(Map<String, String>)? successListener, Function()? failedListener}) async {
    try {
      GlobalDownloadManager downloadManager = GlobalDownloadManager();
      Map<String, String> result = await downloadManager.downloadResources(
        urlList,
        needUnzip: true, // ZIP资源需要解压
      );

      // 调用成功回调
      successListener?.call(result);
    } catch (e) {
      callBackFailedListener("我的奖励页动效资源解压失败: $e",failedListener);
    }
  }

  void callBackFailedListener(String? logTxt, Function()? failedListener) {
    if (failedListener != null) {
      failedListener();
    }
    if (logTxt != null) l.e("促完课活动", logTxt);
  }

  bool isEssentialAnimationResourcesDownloaded(PlanActivityDetailData data, bool notNeedCheek) {
    // 只检查 myGiftList 中的动效资源
    bool allGiftsDownloaded = data.myGiftList.every(
            (gift) => gift.spineResourceVo?.isDowned == true
    );

    if (allGiftsDownloaded || notNeedCheek) {
      l.i("促完课活动", "我的奖励页,动效资源下载完成");
    }

    return allGiftsDownloaded;
  }

  Future<void> getNewStatus(MyGiftModel model, int maxNodeIndex) async {
    if (model.index == maxNodeIndex) {
      String key = "kPlanHomeClassCoursePromoteFinish_myGift_${activityId}_${maxNodeIndex}_${BaseConfig.share.userInfo?.uid}";
      NativeValue? mp = await jojoNativeBridge.operationNativeValueGet(key: key).then((value) => value.data);
      String value = mp?.value ?? '';
      if (value.isEmpty) model.isNew = value.isEmpty;
    }
  }

  Future<void> setNewStatus(int maxNodeIndex) async {
    String key = "kPlanHomeClassCoursePromoteFinish_myGift_${activityId}_${maxNodeIndex}_${BaseConfig.share.userInfo?.uid}";
    await jojoNativeBridge.operationNativeValueSet(key: key, value: "1");
  }

  void setGetTime(MyGiftModel model) {
    if (model.getTime == 0) {
      model.subTitle = "";
    } else {
      try {
        DateTime dateTime = DateTime.fromMillisecondsSinceEpoch(model.getTime);
        String formattedDate = DateFormat('M月d日').format(dateTime);
        model.subTitle = "$formattedDate获得";
      } catch (e) {
        // 处理可能的时间格式异常
        model.subTitle = "";
      }
    }
  }
}
