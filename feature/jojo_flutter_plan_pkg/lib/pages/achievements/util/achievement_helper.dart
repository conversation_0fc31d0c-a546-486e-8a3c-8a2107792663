import 'package:jojo_flutter_base/base.dart';

class AchievementHelper {
  static const String _hasPlayedVoiceTodayKey = "hasPlayedVoiceToday_";

  /// 新获训练营引导是否展示过
  static Future<bool> hasPlayedVoiceToday(int subjectType) async {
    UserInfo? info =
        await jojoNativeBridge.getUserInfo().then((value) => value.data);
    String key = "$_hasPlayedVoiceTodayKey${subjectType}_${info?.uid ?? ''}";
    return getValueFromSp(key).then((value) => value == "true");
  }

  /// 保存新获训练营引导
  static void savePlayedVoiceToday(int subjectType) {
    jojoNativeBridge.getUserInfo().then((value) => value.data).then((value) => {
          saveValueToSp(
              "$_hasPlayedVoiceTodayKey${subjectType}_${value?.uid ?? ''}",
              "true")
        });
  }

  static Future<String?> getValueFromSp(String key) async {
    NativeValue? mp = await jojoNativeBridge
        .operationNativeValueGet(key: key)
        .then((value) => value.data);
    return mp?.value;
  }

  static void saveValueToSp(String key, String value) {
    jojoNativeBridge.operationNativeValueSet(key: key, value: value);
  }

  /// 我的成就/成就详情 首次引导
  static const String _guideKey = 'my_achievements_first_guide';

  static Future<bool> getNeedShowGuide() async {    
    try {
      final result =
          await jojoNativeBridge.operationNativeValueGet(key: _guideKey);
      final guideStatus = result.data?.value ?? "0";
      return Future.value(guideStatus == '1' ? false : true);
    } catch (e) {
      return Future.value(false);
    }
  }

  static saveGuideFinish() {
    jojoNativeBridge.operationNativeValueSet(key: _guideKey, value: "1");
  }
}
