import 'dart:math';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/config/config.dart';
import 'package:jojo_flutter_base/widgets/app_bars/jojo_appbar.dart';
import 'package:jojo_flutter_base/widgets/common/page_loading_v25.dart';
import 'package:jojo_flutter_plan_pkg/common/host_env/host_env.dart';
import 'package:jojo_flutter_plan_pkg/pages/achievements/my_achievements/view.dart';

import '../../../generated/l10n.dart';
import 'controller.dart';
import 'state.dart';

class MyAchievementsPage extends BasePage {
  final int? subjectType;
  final int? partnerId;
  const MyAchievementsPage({super.key, this.subjectType, this.partnerId});

  @override
  State<StatefulWidget> createState() => MyAchievementsPageState();
}

class MyAchievementsPageState extends BaseState<MyAchievementsPage> {
  late ScrollController _scrollController;
  final double _opacityThreshold = 100.0; // 滚动 _opacityThreshold 像素后背景达到完全不透明状态
  final ValueNotifier<double> _appBarOpacityNotifier = ValueNotifier(0.0);

  /// 播放音频相关的变量
  late final AudioPlayer _audioPlayer;

  late MyAchievementsController _controller;
  bool _isPause = false;

  @override
  void initState() {
    super.initState();

    _controller = MyAchievementsController.withDefault(
      subjectType: widget.subjectType!,
      partnerId: widget.partnerId,
    );

    _scrollController = ScrollController();

    // 滚动监听，不使用 setState 直接更新 ValueNotifier
    _scrollController.addListener(() {
      double offset = _scrollController.offset;
      // 计算新的透明度，限制在 0~1 之间
      double newOpacity = min(1, max(0, offset / _opacityThreshold));
      if (_appBarOpacityNotifier.value != newOpacity) {
        _appBarOpacityNotifier.value = newOpacity;
      }
    });

    _audioPlayer = AudioPlayer();
  }

  @override
  void dispose() {
    _appBarOpacityNotifier.dispose();
    _scrollController.dispose();
    _audioPlayer.dispose();
    super.dispose();
  }

  @override
  void onPause() {
    super.onPause();
    _controller.onPause();
    if (_audioPlayer.state == PlayerState.playing) {
      _audioPlayer.pause();
    }
    if (RunEnv.isIOS) {
      JoJoNativeBridge.shared.setInteractivePopEnable(enable: "1");
    }
  }

  @override
  void onResume() {
    super.onResume();
    _controller.onResume();
    if (_audioPlayer.state == PlayerState.paused) {
      _audioPlayer.resume();
    }
    if (RunEnv.isIOS) {
      JoJoNativeBridge.shared.setInteractivePopEnable(enable: "0");
    }
  }

  List<Widget> getAppbarActions() {
    var isTeacherMode = BaseConfig.share.deviceInfo?.grayTeacher == "true";
    return isTeacherMode || kDebugMode
        ? [
            MaterialButton(
              onPressed: () {
                const route =
                    "tinman-router://cn.tinman.jojoread/flutter/plan/teacherAchievementTypes?subjectType=2";
                RunEnv.jumpLink(route);
              },
              child: const Text('审校'),
            )
          ]
        : [];
  }

  String _getTitle(BuildContext context, MyAchievementsState state) {
    var title = state.title;
    if (title != null) {
      if (title.isNotEmpty) {
        return title;
      } else {
        return S.of(context).myAchievements;
      }
    }
    return '';
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (_) {
        assert(widget.subjectType != null, 'subjectType must not be null');
        return _controller;
      },
      child: Builder(
        builder: (innerContext) {
          return BlocBuilder<MyAchievementsController, MyAchievementsState>(
            builder: (context, state) {
              // 当最近有获得奖章或奖章总数为0时，需要设置 extendBodyBehindAppBar 为 true
              bool extendBodyBehindAppBar =
                  (!state.onlyTrainingCamp && state.recentObtainedCount > 0) ||
                      state.medalsCount == 0;

              return Scaffold(
                extendBodyBehindAppBar: extendBodyBehindAppBar,
                appBar: PreferredSize(
                  preferredSize: Size.fromHeight(
                    const JoJoAppBar().preferredSize.height,
                  ),
                  child: ValueListenableBuilder<double>(
                    valueListenable: _appBarOpacityNotifier,
                    builder: (context, opacity, child) {
                      return JoJoAppBar(
                        title: _getTitle(context, state),
                        backgroundColor: Color.fromRGBO(255, 255, 255, opacity),
                        actions: getAppbarActions(),
                      );
                    },
                  ),
                ),
                body: WillPopScope(
                  onWillPop: () async {
                    return false;
                  },
                  child: JoJoPageLoadingV25(
                    scene: PageScene.common,
                    exception: state.exception,
                    hideProgress: true,
                    status: state.pageStatus,
                    child: MyAchievementsView(
                        scrollController: _scrollController,
                        audioPlayer: _audioPlayer),
                    retry: () {
                      context
                          .read<MyAchievementsController>()
                          .setPageStatus(PageStatus.loading);
                      context.read<MyAchievementsController>().refreshData();
                    },
                  ),
                ),
              );
            },
          );
        },
      ),
    );
  }
}
