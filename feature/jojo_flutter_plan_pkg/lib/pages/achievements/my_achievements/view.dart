import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/spine/jojo_spine_animation_control.dart';
import 'package:jojo_flutter_base/spine/jojo_spine_animation_widget.dart';
import 'package:jojo_flutter_base/utils/log.dart';
import 'package:jojo_flutter_base/widgets/common/cached_network_image_pro.dart';
import 'package:jojo_flutter_plan_pkg/pages/achievements/my_achievements/state.dart';
import 'package:jojo_flutter_plan_pkg/pages/achievements/my_achievements/viewmodel/view_model.dart';
import 'package:jojo_flutter_plan_pkg/pages/achievements/my_achievements/widget/cards_row.dart';
import 'package:jojo_flutter_plan_pkg/pages/achievements/my_achievements/widget/category_header.dart';
import 'package:jojo_flutter_plan_pkg/pages/achievements/my_achievements/widget/custom_separator.dart';
import 'package:jojo_flutter_plan_pkg/pages/achievements/my_achievements/widget/empty_view.dart';
import 'package:jojo_flutter_plan_pkg/pages/achievements/my_achievements/widget/latest_medals_widget.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_msg/controller.dart';
import 'dart:async';

import '../../../common/config/config.dart';
import '../../../common/host_env/host_env.dart';
import '../../../static/audio.dart';
import '../../../static/spine.dart';
import '../../plan_home/model/subject_type.dart';
import '../model/medal_model.dart';
import '../util/achievement_helper.dart';
import 'controller.dart';
import 'viewmodel/latest_medal_click_event_data.dart';

class MyAchievementsView extends StatefulWidget {
  final ScrollController scrollController;
  final AudioPlayer audioPlayer;

  const MyAchievementsView(
      {Key? key, required this.scrollController, required this.audioPlayer})
      : super(key: key);

  @override
  State<StatefulWidget> createState() => MyAchievementsViewState();
}

class MyAchievementsViewState extends State<MyAchievementsView> {
  final String tag = 'MyAchievementsViewState';

  /// 最近获得奖章的索引
  int latestMedalsWidgetIndex = 0;

  /// 播放音频相关的变量
  int _currentAudioIndex = 0;
  StreamSubscription<void>? _audioCompletionSubscription;

  MyAchievementsState get _state =>
      context.read<MyAchievementsController>().state;

  late MyAchievementsController _controller;

  Timer? _timer; // 定时器实例
  bool _isPaused = false;
  StreamSubscription? _audioPlayerStateSubscription;

  @override
  void initState() {
    super.initState();
    _controller = context.read<MyAchievementsController>();
    _handleStart();
    RunEnv.sensorsTrack('\$AppViewScreen', _sensorsTrackData());

    _controller.pagePauseNotifier.addListener(() {
      _isPaused = _controller.pagePauseNotifier.value;
      if (_controller.isGuiding) {
        if (_isPaused) {
          _timer?.cancel();
          widget.audioPlayer.stop();
        } else {
          _playGuideAudio();
        }
      }
    });
  }

  _handleStart() async {
    /// 显示首次引导
    await _checkNeedGuide();
    if (_controller.isGuiding) {
      setState(() {});
      return;
    }

    if (_state.mediaPaths.isNotEmpty) {
      /// 播放ai语音引导
      _playAIGCAudio();
    } else {
      /// 播放新奖章引导
      _playNewMedalAudio();
    }
  }

  _playAIGCAudio() {
    if (_state.mediaPaths.isNotEmpty) {
      _currentAudioIndex = 0;
      widget.audioPlayer
          .play(DeviceFileSource(_state.mediaPaths[_currentAudioIndex]));
      _audioCompletionSubscription =
          widget.audioPlayer.onPlayerComplete.listen((event) {
        _currentAudioIndex++;
        if (_currentAudioIndex < _state.mediaPaths.length) {
          widget.audioPlayer
              .play(DeviceFileSource(_state.mediaPaths[_currentAudioIndex]));
        }
      });
    }
  }

  @override
  void dispose() {
    _audioCompletionSubscription?.cancel();
    _spineController.dispose();
    _timer?.cancel();
    _audioPlayerStateSubscription?.cancel();
    super.dispose();
  }

  void stopAudio() {
    widget.audioPlayer.stop();
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        IgnorePointer(
          ignoring: _controller.isGuiding,
          child: _buildViews(context),
        ),
        ..._buildGuideViews(context),
      ],
    );
  }

  Widget _buildViews(BuildContext context) {
    return ListView.builder(
      padding: EdgeInsets.zero,
      controller: widget.scrollController,
      itemCount: _state.items.length,
      itemBuilder: (context, index) {
        MyAchievementsItem item = _state.items[index];
        return _buildListItem(item);
      },
    );
  }

  Widget _buildListItem(MyAchievementsItem item) {
    if (item is LatestMedals) {
      if (item.latestMedals.isEmpty) {
        return const SizedBox.shrink();
      } else {
        return LatestMedalsWidget(item.latestMedals,
            defaultSelectIndex: latestMedalsWidgetIndex, onItemTap: (medal) {
          _sensorClickLatestMedal(medal);
          _gotoMedalDetail(medal);
        }, onSelectedItem: (index, medal) {
          latestMedalsWidgetIndex = index;
          RunEnv.sensorsTrack(
              'ElementView',
              _sensorsTrackData(
                cElementName: "我的奖章_最近获得_滑动列表",
                medalId: "${medal.id ?? 0}",
                medalName: medal.title ?? '',
              ));
        });
      }
    } else if (item is SegmentTitle) {
      // 学段标题
      return CustomSeparator(
        sidePadding: 20.rdp,
        widgetSpacing: 10.rdp,
        lineThickness: 0.3,
        centerWidget: Text(
          item.title,
          style: context.textstyles.remark.pf.copyWith(
            color: context.appColors.jColorGray4,
          ),
        ),
      );
    } else if (item is CategoryTitle) {
      // 分类标题
      return CategoryHeader(title: item.title);
    } else if (item is AchievementsRow) {
      // 勋章行
      return CardsRow(
        cards: item.cards,
        onItemTap: (card) {
          int medalId = 0;
          String medalName = '';
          if (card.latestMedal != null) {
            medalId = card.latestMedal?.id ?? 0;
            medalName = card.latestMedal?.title ?? '';

            _gotoMedalDetail(card.latestMedal);
          } else {
            medalId = card.nextStageMedal?.id ?? 0;
            medalName = card.nextStageMedal?.title ?? '';

            _gotoMedalDetail(card.nextStageMedal);
          }

          RunEnv.sensorsTrack(
              '\$AppClick',
              _sensorsTrackData(
                elementName: "我的奖章_奖章列表_点击奖章",
                medalId: medalId.toString(),
                medalName: medalName,
              ));
        },
      );
    } else if (item is FillImage) {
      // 占位图
      return ImageNetworkCached(
        imageUrl: item.image,
        width: double.infinity,
        fit: BoxFit.cover,
      );
    } else if (item is LineSpace) {
      // 占位行
      return SizedBox(
        height: item.height.clamp(0.0, double.infinity),
      );
    } else if (item is EmptyTip) {
      // 缺省页
      return const EmptyView();
    } else {
      l.i(tag, '未知的列表项: $item');
      return Text('未知的列表项: $item');
    }
  }

  /// 最近获得_点击奖章埋点
  _sensorClickLatestMedal(MedalModel medal) {
    RunEnv.sensorsTrack(
        '\$AppClick',
        _sensorsTrackData(
          elementName: "我的奖章_最近获得_点击奖章",
          medalId: "${medal.id ?? 0}",
          medalName: medal.title ?? '',
        ));
  }

  /// 跳转到勋章详情页
  _gotoMedalDetail(MedalModel? medal) {
    final isVisitorAttitude = pageController?.isVisitorAttitude() ?? false;
    l.i("isVisitorAttitude", isVisitorAttitude);
    if (isVisitorAttitude) {
      return; //如果是客态页就 return
    }
    stopAudio();

    jojoEventBus.fire(LatestMedalClickEventData(medal?.id ?? 0));

    RunEnv.jumpLink(medal?.detailRoute ?? '');
  }

  Map<String, dynamic> _sensorsTrackData({
    String? elementName,
    String? cElementName,
    String? courseStage,
    String? materialId,
    String? medalId,
    String? medalName,
  }) {
    Map<String, dynamic> map = {
      "\$screen_name": "我的奖章页面曝光",
      "custom_state": _state.onlyTrainingCamp ? "纯训练营用户" : "包含年课的用户",
      "material_id":
          getSubjectName(context.read<MyAchievementsController>().subjectType),
      if (cElementName != null) "c_element_name": cElementName,
      if (elementName != null) "\$element_name": elementName,
      if (courseStage != null) "course_stage": courseStage,
      if (materialId != null) "material_id": materialId,
      if (medalId != null) "medal_id": medalId,
      if (medalName != null) "medal_name": medalName,
    };
    return map;
  }

  MyAchievementsController? get pageController =>
      mounted ? context.read<MyAchievementsController>() : null;

  final _spineController = JoJoSpineAnimationController();

  _checkNeedGuide() async {
    var needGuide = await AchievementHelper.getNeedFirstGuide();
    var medal = _getLastedMedal();
    if (needGuide && medal != null) {
      setState(() {
        _controller.isGuiding = needGuide;
      });
    }
  }

  List<Widget> _buildGuideViews(BuildContext context) {
    return [
      /// 引导动画小手
      Positioned(
        top: 215.rdp,
        left: MediaQuery.of(context).size.width / 2 + 5.rdp,
        child: _buildGuideAnimaSpine(),
      ),

      /// 引导时可点击区域
      Positioned(
        top: 150.rdp,
        left: MediaQuery.of(context).size.width / 2 - 70.rdp,
        child: Container(
          color: Colors.transparent,
          width: 140.rdp,
          height: 140.rdp,
          child: GestureDetector(
            onTap: () {
              _firstGuideClick();
            },
          ),
        ),
      ),
    ];
  }

  /// 引导时点击奖章，埋点并跳转详情
  _firstGuideClick() {
    _timer?.cancel();
    widget.audioPlayer.stop();
    var medal = _getLastedMedal();
    if (medal != null) {
      _sensorClickLatestMedal(medal);
      _gotoMedalDetail(medal);
      AchievementHelper.saveFirstGuideFinish();
    }

    setState(() {
      _controller.isGuiding = false;
    });
  }

  MedalModel? _getLastedMedal() {
    var item =
        _state.items.firstWhereOrNull((element) => element is LatestMedals);
    if (item != null && item is LatestMedals && item.latestMedals.isNotEmpty) {
      var index = latestMedalsWidgetIndex < item.latestMedals.length
          ? latestMedalsWidgetIndex
          : 0;
      return item.latestMedals[index];
    }
    return null;
  }

  Widget _buildGuideAnimaSpine() {
    if (!_controller.isGuiding) {
      return Container();
    }

    return Container(
      alignment: Alignment.bottomRight,
      height: 80.rdp,
      width: 80.rdp,
      child: JoJoSpineAnimationWidget(
        AssetsSpine.POP_FINGER_TIPS_ATLAS,
        AssetsSpine.POP_FINGER_TIPS_SKEL,
        LoadMode.assets,
        _spineController,
        package: Config.package,
        onInitialized: (controller) {
          if (mounted) {
            controller.skeleton
              ..setScaleX(1.2)
              ..setScaleY(1.2);
            _spineController.playAnimation(JoJoSpineAnimation(
              animaitonName: "play",
              trackIndex: 0,
              loop: true,
              listener: (type) {
                if (type == AnimationEventType.start) {
                  _playGuideAudio();
                }
              },
            ));
          }
        },
      ),
    );
  }

  /// 如果有未查看的奖章，播放引导音效
  _playNewMedalAudio() {
    var needNewMedalGuide = false;
    for (var item in _state.items) {
      if (item is LatestMedals) {
        for (var medal in item.latestMedals) {
          if (medal.isView == 0) {
            needNewMedalGuide = true;
            break;
          }
        }
      } else if (item is AchievementsRow) {
        for (var card in item.cards) {
          if (card.latestMedal?.isView == 0) {
            needNewMedalGuide = true;
            break;
          }
        }
      }
    }

    if (needNewMedalGuide) {
      _playLocalAudio(AssetsAudio.MY_ACHIEVEMENTS_GUIDE_CHECK_NEW);
    }
  }

  _startGuideTimer() {
    if (_isPaused) {
      return;
    }

    _timer?.cancel();
    _timer = Timer(const Duration(seconds: 2), () {
      _playGuideAudio();
    });
  }

  _playGuideAudio() {
    if (!_controller.isGuiding) return;
    _playLocalAudio(AssetsAudio.MY_ACHIEVEMENTS_GUIDE_LATEST);
  }

  _playLocalAudio(String audioPath) {
    widget.audioPlayer.stop();

    String? package = Config.package;
    String keyName =
        package == null ? audioPath : 'packages/$package/$audioPath';
    widget.audioPlayer.audioCache.prefix = '';
    _audioPlayerStateSubscription ??= widget.audioPlayer.onPlayerStateChanged.listen((event) {
        if (event == PlayerState.completed) {
          _startGuideTimer();
        }
      });
    widget.audioPlayer.play(AssetSource(keyName));
  }
}
