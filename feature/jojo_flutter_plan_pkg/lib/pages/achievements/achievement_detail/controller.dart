import 'dart:async';
import 'dart:io';
import 'dart:math';

import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/download/jojo_download.dart';
import 'package:jojo_flutter_base/utils/log.dart';
import 'package:jojo_flutter_base/widgets/common/page_loading_v25.dart';
import 'package:jojo_flutter_plan_pkg/pages/achievements/achievement_detail/state.dart';

import '../../../model/aigc_medias.dart';
import '../../../service/achievements_api.dart';
import '../../../service/aigc_medias_api.dart';
import '../../../utils/file_util.dart';
import '../model/achievement_detail_model.dart'  hide UserInfo;
import '../model/medal_model.dart';


class AchievementDetailController extends Cubit<AchievementDetailState> {
  static const String tag = "AchievementDetailController";

  final int medalId;
  final AchievementsApi dao;
  UserInfo? userInfo;
  final JoJoResourceManager _resourceManager = JoJoResourceManager();

  /// 首次引导中
  bool isGuiding = false;

  AchievementDetailController({
    required this.medalId,
    required this.dao,
  }) : super(AchievementDetailState(PageStatus.loading));

  AchievementDetailController.withDefault({required this.medalId})
      : dao = proAchievementsApi,
        // : dao = AchievementsApiMock(),
        super(AchievementDetailState(PageStatus.loading)) {
    fetchUserInfo();      
    refreshData();
  }

  setPageStatus(PageStatus status) {
    emit(AchievementDetailState(status));
  }

  fetchUserInfo() async {
   userInfo  =
        await jojoNativeBridge.getUserInfo().then((value) => value.data);
  }

  Future<void> refreshData() async {
    try {
      // 获取数据
      final AchievementDetailModel model = await dao.getMedalDetail(medalId);
      if (model.medalGroup == null) {
        l.e(tag, "没有奖章数据");
        emit(AchievementDetailState(PageStatus.error,
            exception: Exception("没有奖章数据")));
        return;
      }

      final groupMedals = model.medalGroup!.groupMedals ?? [];
      final commonRes = model.commonRes ?? "";
      int initialIndex = 0;
      MedalModel? displayMedal;
      final List<MedalModel> displayMedals = [];

      // 遍历奖章列表，并记录对应 medalId 的索引
      for (int i = 0; i < groupMedals.length; i++) {
        final medal = groupMedals[i];
        if (medal.id == medalId) {
          initialIndex = i;
          displayMedal = medal;
        }
        displayMedals.add(medal);
        // 只展示第一个未获得的奖章
        if (medal.isGet == 0) break;
      }

      List<String> mediaPaths = [];
      String localMediaPath = "";

      if (displayMedal?.isGet == 0) {
        mediaPaths = await _tryGetAigcMedia(displayMedals.last);
        l.i(tag, "获取 AIGC 语音成功: $mediaPaths");
      } else {
        final isRare =
            displayMedal?.tags?.any((element) => element.name == "稀有") ?? false;
        final randomNum = Random().nextInt(4) + 1;
        localMediaPath = isRare
            ? "assets/audio/medal_audios/medal_special_00$randomNum.mp3"
            : "assets/audio/medal_audios/medal_general_00$randomNum.mp3";
        l.i(tag, "获取本地语音成功: $localMediaPath");
      }

      // 下载压缩包资源
      _downloadZipRes(
        groupMedals,
        commonRes,
        successListener: (urlMap) {
          emit(AchievementDetailState(
            PageStatus.success,
            model: model,
            commonResZip: urlMap[commonRes] ?? "",
            aiMediaPaths: mediaPaths,
            localMediaPath: localMediaPath,
            initialIndex: initialIndex,
            medals: displayMedals,
          ));
        },
        failListener: (error) {
          l.e(tag, "下载资源失败: $error");
          emit(AchievementDetailState(PageStatus.error,
              exception: Exception(error)));
        },
      );
    } catch (e) {
      l.e(tag, "刷新数据异常: $e");
      if (e is Exception) {
        emit(AchievementDetailState(PageStatus.error, exception: e));
      } else {
        emit(AchievementDetailState(PageStatus.error, exception: Exception(e)));
      }
    }
  }

  /// 获取 AIGC 语音，并将多个下载任务合并为一次下载
  Future<List<String>> _tryGetAigcMedia(MedalModel nextStageMedal) async {
    try {
      final AigcMedias aigcMedias = await aigcMediasApi.getSubjectMedals([
        {
          "sceneKey": "MEDAL_REMARK",
          "sceneParams": {
            "medalKey": "${nextStageMedal.id ?? 0}",
            "MedalName": nextStageMedal.title ?? "",
          }
        }
      ]);

      // 收集所有有效的 URL
      final List<String> urls = [];
      for (final sceneResult in (aigcMedias.sceneResults ?? [])) {
        for (final media in (sceneResult.medias ?? [])) {
          if ((media.url ?? "").isNotEmpty) {
            urls.add(media.url!);
          }
        }
      }
      if (urls.isEmpty) {
        l.w(tag, "AIGC媒体URL列表为空，返回空结果");
        return [];
      }

      final Completer<List<String>> completer = Completer<List<String>>();
      final List<String> paths = List.generate(urls.length, (index) => "");

      await _resourceManager.downloadUrl(
        urls,
        isNeedCancel: false,
        successListener: (urlMap) {
          for (int i = 0; i < urls.length; i++) {
            final url = urls[i];
            final path = urlMap[url] ?? "";
            paths[i] = path;
          }
          if (!completer.isCompleted) completer.complete(paths);
        },
        failListener: (error) {
          l.e(tag, "下载语音失败: $error");
          if (!completer.isCompleted) completer.complete([]);
        },
      );

      return completer.future;
    } catch (e) {
      l.e(tag, "获取 AIGC 语音异常: $e");
      return [];
    }
  }

  /// 下载奖章资源的压缩包及公共资源
  Future<void> _downloadZipRes(
    List<MedalModel> medals,
    String commonResUrl, {
    Function(Map<String, String>)? successListener,
    Function(String)? failListener,
  }) async {
    // 收集奖章资源 URL
    final List<String> urlList = medals
        .map((medal) => medal.resource?["flutterRes"] ?? "")
        .where((url) => url.isNotEmpty)
        .toList();

    // 添加公共资源 URL
    if (commonResUrl.isNotEmpty) {
      urlList.add(commonResUrl);
    }

    await _resourceManager.downloadUrl(
      urlList,
      isNeedCancel: false,
      successListener: (urlMap) async {
        for (final url in urlList) {
          final String localPath = urlMap[url] ?? "";
          if (localPath.isNotEmpty) {
            final String dirPath = await unzip(localPath);
            if (Directory(dirPath).existsSync()) {
              l.i(tag,
                  "_downloadZipRes success url: $url localPath: $localPath");
            } else {
              l.e(tag, "_downloadZipRes fail url: $url localPath: $localPath");
            }
          } else {
            l.e(tag, "_downloadZipRes fail url: $url");
          }
        }
        if (successListener != null) {
          successListener(urlMap);
        }
      },
      failListener: (error) {
        l.e(tag, "_downloadZipRes fail error: $error");
        if (failListener != null) {
          failListener(error.message??"");
        }
      },
    );
  }
}
