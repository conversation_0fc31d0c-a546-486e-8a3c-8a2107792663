import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/utils/hex_color.dart';
import 'package:jojo_flutter_base/widgets/cached_network_image.dart';
import 'package:jojo_flutter_base/widgets/common/image_asset_pro.dart';
import 'package:jojo_flutter_plan_pkg/common/host_env/host_env.dart';
import 'package:jojo_flutter_plan_pkg/generated/l10n.dart';
import 'package:jojo_flutter_plan_pkg/pages/jojo_continuology/calendar_utils.dart';
import 'package:jojo_flutter_plan_pkg/pages/jojo_continuology/jojo_continuology_controller.dart';
import 'package:jojo_flutter_plan_pkg/pages/jojo_continuology/model/calendar_lesson_info_model.dart';
import 'package:jojo_flutter_plan_pkg/pages/jojo_continuology/model/jojo_continuology_api_data.dart';
import 'package:jojo_flutter_plan_pkg/pages/jojo_continuology/widget/calendar_study_info_btn.dart';
import 'package:jojo_flutter_plan_pkg/pages/jojo_continuology/widget/calendar_study_info_header.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_msg/controller.dart';
import 'package:jojo_flutter_plan_pkg/static/img.dart';
import 'package:jojo_flutter_plan_pkg/utils/file_util.dart';

/// 点击连续学日历某天时展示的Widget
class CalendarStudyInfoWidget extends StatefulWidget {
  final CalendarContentVo calendarVo;
  final CalendarDayItemVo? selectDayItem; // 当前选中的日期
  final bool visibility; // 是否可见

  const CalendarStudyInfoWidget(
      {Key? key,
      required this.calendarVo,
      required this.selectDayItem,
      required this.visibility})
      : super(key: key);

  @override
  State<CalendarStudyInfoWidget> createState() =>
      _CalendarStudyInfoWidgetState();
}

class _CalendarStudyInfoWidgetState extends State<CalendarStudyInfoWidget>
    with TickerProviderStateMixin {
  late Color _subjectColor; // 科目色
  late Color _cardBgColor; // 卡片主色
  late AnimationController _controller;
  late Animation<double> _positionAnimation;
  late Animation<double> _opacityAnimation;
  JoJoContinuologyController? _ctrl;

  @override
  initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    // 定义动画的起始和结束位置
    _positionAnimation =
        Tween<double>(begin: 50.rdp, end: 0).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeOut,
    ));
    // 定义动画的起始和结束透明度
    _opacityAnimation =
        Tween<double>(begin: 0.0, end: 1.0).animate(_controller);
    // 添加监听器以更新 UI
    _controller.addListener(() {
      setState(() {});
    });
    if (widget.visibility) {
      _controller.reset();
      _controller.forward();
    }
  }

  @override
  void didUpdateWidget(covariant CalendarStudyInfoWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.visibility == false &&
        widget.visibility == true &&
        oldWidget.selectDayItem != widget.selectDayItem) {
      _controller.reset();
      _controller.forward();
    }
  }

  @override
  void dispose() {
    _controller.removeListener(() {});
    _controller.dispose();
    super.dispose();
  }

  // 待解锁
  Widget _buildLockWidget(
      LessonInfoWidgetModel model, CalendarStudyStatus status) {
    model.type = StudyBtnType.study;
    model.status = StudyBtnStatusType.lock;
    // 确定开课时间
    DateTime unlockTime =
        DateTime.fromMillisecondsSinceEpoch(model.lessonInfo.unlockTime ?? 0);
    model.lessonShowInfo!.title =
        "${unlockTime.month}${S.of(context).month}${unlockTime.day}${S.of(context).day}${S.of(context).classStart}";
    model.lessonShowInfo!.btnText = S.of(context).waitStartClass;

    return Container(
      padding: EdgeInsets.only(
          left: ConfigSize.contentSpace,
          right: ConfigSize.contentSpace,
          top: ConfigSize.classInfoTopSpace,
          bottom: ConfigSize.classInfoBottomSpace),
      child: Column(
        children: [
          CalendarLessonHeaderWidget(
              calendarVo: widget.calendarVo,
              status: status,
              lessonName: model.lessonShowInfo?.lessonHeaderDisplayName ?? '',
              subjectColor: _subjectColor),
          SizedBox(
            height: 8.rdp,
          ),
          CalendarLessonStudyBtnWidget(
            calendarVo: widget.calendarVo,
            model: model,
            subjectColor: _subjectColor,
            btnTextColor: context.appColors.colorVariant6(_subjectColor),
            btnBgColor: context.appColors.colorVariant2(_subjectColor),
            isShowArrowRight: false,
          ),
        ],
      ),
    );
  }

  // 已学完
  Widget _buildFinishWidget(
      LessonInfoWidgetModel model, CalendarStudyStatus status) {
    String jojoText = widget.calendarVo.isJoJo
        ? "${S.of(context).goToLight}${S.of(context).JoJo}"
        : "${S.of(context).goToLight}${S.of(context).greenPulse}";
    bool isFinish = model.lessonInfo.isFinish == 1;
    model.type = StudyBtnType.study;
    model.status = StudyBtnStatusType.enable;
    model.lessonShowInfo!.title = isFinish ? S.of(context).alreadyLight : jojoText;
    model.lessonShowInfo!.btnText =
        isFinish ? S.of(context).weAgingStudy : S.of(context).goStudy;
    model.lessonShowInfo!.titleIcon = findFilesByExtension(
            widget.calendarVo.spineResource, "calendar_icon_dy.png")
        ?.path;
    model.elementName = "日历点位_${model.lessonShowInfo!.btnText}";
    model.customState = isFinish ? "连胜叫" : "已点亮";
    return Container(
      padding: EdgeInsets.only(
          left: ConfigSize.contentSpace,
          right: ConfigSize.contentSpace,
          top: ConfigSize.classInfoTopSpace,
          bottom: ConfigSize.classInfoBottomSpace),
      child: Column(
        children: [
          CalendarLessonHeaderWidget(
              calendarVo: widget.calendarVo,
              status: status,
              lessonName: model.lessonShowInfo?.lessonHeaderDisplayName ?? '',
              subjectColor: _subjectColor),
          SizedBox(
            height: 8.rdp,
          ),
          CalendarLessonStudyBtnWidget(
              calendarVo: widget.calendarVo,
              model: model,
              subjectColor: _subjectColor,
              btnBgColor: Colors.white,
              btnTextColor: context.appColors.jColorYellow6,
              isShowArrowRight: false),
        ],
      ),
    );
  }

  // 待点亮（显示去学习）
  Widget _buildUnFinishWidget(
      LessonInfoWidgetModel model, CalendarStudyStatus status,
      {bool canShowGuide = false}) {
    model.lessonShowInfo!.title = widget.calendarVo.isJoJo
        ? "${S.of(context).goToLight}${S.of(context).JoJo}"
        : "${S.of(context).goToLight}${S.of(context).greenPulse}";
    model.lessonShowInfo!.btnText = S.of(context).toLight;
    model.lessonShowInfo!.titleIcon = findFilesByExtension(
            widget.calendarVo.spineResource, "calendar_icon_dy.png")
        ?.path;
    model.elementName = "日历点位_${model.lessonShowInfo!.btnText}";
    model.customState = "待点亮";
    return Container(
      padding: EdgeInsets.only(
          left: ConfigSize.contentSpace,
          right: ConfigSize.contentSpace,
          top: ConfigSize.classInfoTopSpace,
          bottom: ConfigSize.classInfoBottomSpace),
      child: Column(
        children: [
          CalendarLessonHeaderWidget(
              calendarVo: widget.calendarVo,
              status: status,
              lessonName: model.lessonShowInfo?.lessonHeaderDisplayName ?? '',
              subjectColor: _subjectColor),
          SizedBox(
            height: 8.rdp,
          ),
          CalendarLessonStudyBtnWidget(
              calendarVo: widget.calendarVo,
              model: model,
              subjectColor: _subjectColor,
              btnBgColor: context.appColors.colorVariant4(_subjectColor),
              btnTextColor: Colors.white,
              isShowArrowRight: true,
              canShowGuide: canShowGuide),
        ],
      ),
    );
  }

  // 冰冻叫叫（显示补学按钮、连胜道具信息）
  Widget _buildFreezedWidget(
      LessonInfoWidgetModel model, CalendarStudyStatus status,
      {bool canShowGuide = false}) {
    model.lessonShowInfo!.title = widget.calendarVo.isJoJo
        ? "${S.of(context).repairStudy}${S.of(context).JoJo}"
        : "${S.of(context).repairStudy}${S.of(context).greenPulse}";
    model.lessonShowInfo!.btnText = S.of(context).toRepirStudy;
    model.lessonShowInfo!.titleIcon = findFilesByExtension(
            widget.calendarVo.spineResource, "calendar_icon_dy.png")
        ?.path;
    model.elementName = "日历点位_${model.lessonShowInfo!.btnText}";
    model.customState = "冰冻叫";

    // 如果是训练营，不需要展示道具信息
    if (widget.calendarVo.onlyTrainingCamp) {
      return Container(
        padding: EdgeInsets.only(
            left: ConfigSize.contentSpace,
            right: ConfigSize.contentSpace,
            top: ConfigSize.classInfoTopSpace,
            bottom: ConfigSize.classInfoBottomSpace),
        child: Column(
          children: [
            CalendarLessonHeaderWidget(
                calendarVo: widget.calendarVo,
                status: status,
                lessonName: model.lessonShowInfo?.lessonHeaderDisplayName ?? '',
                subjectColor: _subjectColor),
            SizedBox(
              height: 8.rdp,
            ),
            CalendarLessonStudyBtnWidget(
              calendarVo: widget.calendarVo,
              model: model,
              subjectColor: _subjectColor,
              canShowGuide: canShowGuide,
              btnBgColor: context.appColors.colorVariant4(_subjectColor),
              btnTextColor: Colors.white,
              isShowArrowRight: true,
            ),
          ],
        ),
      );
    }

    // 处理连胜道具信息
    String title = S.of(context).consecutiveWinMessage;
    bool isUseConcatProp = _ctrl?.selectDayItem?.isUseConcatProp == true;
    String btnTxt = isUseConcatProp
        ? S.of(context).youConnected
        : S.of(context).toConsecutiveWin;
    LessonInfoWidgetModel lsToolModel = LessonInfoWidgetModel.getModel(
        widget.calendarVo.lsToolVo,
        model.lessonInfo,
        title,
        btnTxt,
        isUseConcatProp);
    lsToolModel.elementName = "日历点位_连结道具";
    lsToolModel.customState = "冰冻叫";
    lsToolModel.lessonShowInfo!.titleIcon = findFilesByExtension(
            widget.calendarVo.spineResource, "calendar_icon_consecutive.png")
        ?.path;

    return Container(
      padding: EdgeInsets.only(
          left: ConfigSize.contentSpace,
          right: ConfigSize.contentSpace,
          top: ConfigSize.classInfoTopSpace,
          bottom: ConfigSize.classInfoBottomSpace),
      child: Column(
        children: [
          CalendarLessonHeaderWidget(
              calendarVo: widget.calendarVo,
              status: status,
              lessonName: model.lessonShowInfo?.lessonHeaderDisplayName ?? '',
              subjectColor: _subjectColor),
          SizedBox(
            height: 8.rdp,
          ),
          CalendarLessonStudyBtnWidget(
            calendarVo: widget.calendarVo,
            model: model,
            subjectColor: _subjectColor,
            btnBgColor: context.appColors.colorVariant4(_subjectColor),
            btnTextColor: Colors.white,
            isShowArrowRight: true,
            canShowGuide: canShowGuide,
          ),
          SizedBox(
            height: 8.rdp,
          ),
          CalendarLessonStudyBtnWidget(
            calendarVo: widget.calendarVo,
            model: lsToolModel,
            subjectColor: _subjectColor,
            btnBgColor: lsToolModel.status == StudyBtnStatusType.usedTool
                ? context.appColors.colorVariant4(_subjectColor)
                : Colors.white,
            btnTextColor: lsToolModel.status == StudyBtnStatusType.usedTool
                ? context.appColors.colorVariant6(_subjectColor)
                : context.appColors.jColorYellow6,
            isShowArrowRight: false,
          )
        ],
      ),
    );
  }

  // 嗝屁叫叫（显示补学按钮，复活、连胜道具信息）
  Widget _buildDeadWidget(
      LessonInfoWidgetModel model, CalendarStudyStatus status) {
    model.lessonShowInfo!.title = widget.calendarVo.isJoJo
        ? "${S.of(context).repairStudy}${S.of(context).JoJo}"
        : "${S.of(context).repairStudy}${S.of(context).greenPulse}";
    model.lessonShowInfo!.btnText = S.of(context).repairStudy;
    model.elementName = "日历点位_${model.lessonShowInfo!.btnText}";
    model.customState = "冰冻叫";

    // 如果是训练营，不需要展示道具信息
    if (widget.calendarVo.onlyTrainingCamp) {
      return Container(
        padding: EdgeInsets.only(
            left: ConfigSize.contentSpace,
            right: ConfigSize.contentSpace,
            top: ConfigSize.classInfoTopSpace,
            bottom: ConfigSize.classInfoBottomSpace),
        child: Column(
          children: [
            CalendarLessonHeaderWidget(
                calendarVo: widget.calendarVo,
                status: status,
                lessonName: model.lessonShowInfo?.lessonHeaderDisplayName ?? '',
                subjectColor: _subjectColor),
            SizedBox(
              height: 8.rdp,
            ),
            CalendarLessonStudyBtnWidget(
              calendarVo: widget.calendarVo,
              model: model,
              subjectColor: _subjectColor,
              btnBgColor: context.appColors.colorVariant4(_subjectColor),
              btnTextColor: Colors.white,
              isShowArrowRight: true,
            ),
          ],
        ),
      );
    }

    // 处理连胜道具信息
    String title1 = S.of(context).consecutiveWinMessage;
    bool isUseConcatProp = _ctrl?.selectDayItem?.isUseConcatProp == true;
    String btnTxt1 = isUseConcatProp
        ? S.of(context).youConnected
        : S.of(context).toConsecutiveWin;
    LessonInfoWidgetModel lsToolModel = LessonInfoWidgetModel.getModel(
        widget.calendarVo.lsToolVo,
        model.lessonInfo,
        title1,
        btnTxt1,
        isUseConcatProp);
    lsToolModel.elementName = "日历点位_连胜道具";
    lsToolModel.customState = "嗝屁叫";
    lsToolModel.lessonShowInfo!.titleIcon = findFilesByExtension(
            widget.calendarVo.spineResource, "calendar_icon_consecutive.png")
        ?.path;
    // 处理复活道具信息
    String title2 = S.of(context).reviveRepairJOJO;
    String btnTxt2 = S.of(context).toRevive;
    LessonInfoWidgetModel fhToolModel = LessonInfoWidgetModel.getModel(
        widget.calendarVo.fhToolVo, model.lessonInfo, title2, btnTxt2, false);
    fhToolModel.elementName = "日历点位_复活道具";
    fhToolModel.customState = "嗝屁叫";
    fhToolModel.lessonShowInfo!.titleIcon = findFilesByExtension(
            widget.calendarVo.spineResource, "calendar_icon_dy.png")
        ?.path;
    model.lessonShowInfo!.title = "";
    model.lessonShowInfo!.btnText = S.of(context).toRepirStudy;
    return Container(
      padding: EdgeInsets.only(
          left: ConfigSize.contentSpace,
          right: ConfigSize.contentSpace,
          top: ConfigSize.classInfoTopSpace,
          bottom: ConfigSize.classInfoBottomSpace),
      child: Column(
        children: [
          CalendarLessonHeaderWidget(
              calendarVo: widget.calendarVo,
              status: status,
              lessonName: model.lessonShowInfo?.lessonHeaderDisplayName ?? '',
              subjectColor: _subjectColor),
          SizedBox(
            height: 8.rdp,
          ),
          CalendarLessonStudyBtnWidget(
            calendarVo: widget.calendarVo,
            model: fhToolModel,
            subjectColor: _subjectColor,
            btnBgColor: fhToolModel.status == StudyBtnStatusType.usedTool
                ? context.appColors.colorVariant2(_subjectColor)
                : Colors.white,
            btnTextColor: context.appColors.jColorYellow6,
            isShowArrowRight: false,
          ),
          SizedBox(
            height: 8.rdp,
          ),
          CalendarLessonStudyBtnWidget(
            calendarVo: widget.calendarVo,
            model: model,
            subjectColor: _subjectColor,
            btnBgColor: context.appColors.colorVariant4(_subjectColor),
            btnTextColor: Colors.white,
            isShowArrowRight: true,
          ),
          SizedBox(
            height: 16.rdp,
          ),
          CalendarLessonStudyBtnWidget(
            calendarVo: widget.calendarVo,
            model: lsToolModel,
            subjectColor: _subjectColor,
            btnBgColor: lsToolModel.status == StudyBtnStatusType.usedTool
                ? context.appColors.colorVariant2(_subjectColor)
                : Colors.white,
            btnTextColor: context.appColors.jColorYellow6,
            isShowArrowRight: false,
          ),
        ],
      ),
    );
  }

  Widget _buildWaitingWidget(BuildContext context) {
    TextStyle style = context.textstyles.bodyText.pf
        .copyWith(color: context.appColors.colorVariant6(_subjectColor));
    final assestName = widget.calendarVo.isJoJo
        ? AssetsImg.PLAN_CONTINUOLOGY_CALENDAR_GUIDE_JOJO
        : AssetsImg.PLAN_CONTINUOLOGY_CALENDAR_GUIDE_GREEN_BEAN;
  
    bool isPendingStart =
        widget.calendarVo.guideStatus == CalendarGuideStatus.pendingStart.value;
    final firstText = isPendingStart
        ? S.of(context).classStartingSoon
        : S.of(context).studyEndMessage;
    final secondText = isPendingStart
        ? S.of(context).studyPrepGuideMessage
        : S.of(context).waitOpenNewPlanMessage;

    return Container(
      padding: EdgeInsets.symmetric(
          horizontal: ConfigSize.contentSpace, vertical: ConfigSize.classInfoTopSpace),
      decoration: BoxDecoration(
        color: context.appColors.colorVariant1(_subjectColor),
        borderRadius: BorderRadius.circular(context.dimensions.largeCornerRadius.rdp),
        border: Border.all(
            width: 1.0.rdp,
            color: context.appColors.colorVariant2(_subjectColor)),
      ),
      child: Row(
          children: [
            ImageAssetWeb(
              assetName: assestName,
              width: 60.rdp,
              height: 60.rdp,
              package: RunEnv.package,
            ),
            SizedBox(width: context.dimensions.smallSpacing.rdp),
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  firstText,
                  style: style,
                ),
                Text(
                  secondText,
                  style: style,
                ),
              ],
            ),
          ],
        ),
    );
  }

  @override
  Widget build(BuildContext context) {
    _subjectColor = HexColor(widget.calendarVo.subjectColor);
    _cardBgColor = context.appColors.colorVariant1(_subjectColor);

    List<Widget> list = [];
    if (widget.calendarVo.guideStatus == CalendarGuideStatus.pendingStart.value ||
        widget.calendarVo.guideStatus == CalendarGuideStatus.courseEnded.value) {
          list.add(_buildWaitingWidget(context));
    } else {
      _ctrl = context.read<JoJoContinuologyController>();
      List<LessonInfo> lessonInfoList = _ctrl?.selectDayItem?.classList ?? [];
      LessonInfo? firstUnLightOrFrozenLesson = lessonInfoList.firstWhereOrNull(
          (element) =>
              element.status == LessonStatus.unLight ||
              element.status == LessonStatus.freezed);
      for (var element in lessonInfoList) {
        if (_ctrl!.selectDayItem?.status != null) {
          CalendarStudyStatus lessonStatus =
              LessonInfoWidgetModel.getLessonStatus(element);
          LessonInfoWidgetModel model = LessonInfoWidgetModel.getInfoModel(
              element, _ctrl?.selectDayItem?.dateTime, context);
          if (lessonStatus.isLock) {
            // 待解锁
            list.add(_buildLockWidget(model, lessonStatus));
          } else if (lessonStatus.isUnLight) {
            bool canShowGuide = false;
            if (firstUnLightOrFrozenLesson != null) {
              canShowGuide =
                  element.lessonId == firstUnLightOrFrozenLesson.lessonId;
            }
            // 待点亮
            list.add(_buildUnFinishWidget(model, lessonStatus,
                canShowGuide: canShowGuide));
          } else if (lessonStatus.isFrozen) {
            bool canShowGuide = false;
            if (firstUnLightOrFrozenLesson != null) {
              canShowGuide =
                  element.lessonId == firstUnLightOrFrozenLesson.lessonId;
            }
            // 冰冻叫叫
            list.add(_buildFreezedWidget(model, lessonStatus,
                canShowGuide: canShowGuide));
          } else if (lessonStatus.isDead) {
            // 嗝屁叫叫
            list.add(_buildDeadWidget(model, lessonStatus));
          } else if (lessonStatus.isRest) {
            // 休息
            list.add(Container());
          } else if (lessonStatus.isFinish || lessonStatus.isGood) {
            // 普通
            list.add(_buildFinishWidget(model, lessonStatus));
          }
        }
      }
    }
  
    return Visibility(
      visible: widget.visibility,
      maintainState: true,
      child: Transform.translate(
        offset: Offset(0, _positionAnimation.value),
        child: Opacity(
          opacity: _opacityAnimation.value,
          child: Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.all(
                  Radius.circular(ConfigSize.calendarBorderRadius)),
              color: _cardBgColor,
              border: Border.all(
                color: _cardBgColor.withOpacity(0.5),
                width: 0.5.rdp,
              ),
            ),
            child: Column(
              children: list,
            ),
          ),
        ),
      ),
    );
  }
}
