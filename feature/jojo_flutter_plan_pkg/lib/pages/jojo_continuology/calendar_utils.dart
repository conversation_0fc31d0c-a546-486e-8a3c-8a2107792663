import 'dart:io';

import 'package:flutter/material.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/config/config.dart';
import 'package:jojo_flutter_base/utils/log.dart';
import 'package:jojo_flutter_plan_pkg/pages/jojo_continuology/jojo_continuology_controller.dart';
import 'package:jojo_flutter_plan_pkg/pages/jojo_continuology/model/jojo_continuology_api_data.dart';
import 'package:jojo_flutter_plan_pkg/pages/jojo_continuology/model/jojo_continuology_data.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_home/customization/calender_utils.dart';
import 'package:jojo_flutter_plan_pkg/utils/file_util.dart';

const int CONCAT_PROP_ENABLED = 1;

class CalendarContentVo {
  bool isJoJo = true; // 是否是叫叫（绿：思维、英语  叫叫：阅读、小作家、美育）
  bool inWins = false;  // 用于标记休息日的连胜状态
  bool onlyTrainingCamp = false;  // 是否是训练营
  String toolsTitle = ""; // 道具栏标题
  String spineResource = ""; // spine资源地址
  String subjectColor = ""; // 科目色
  String subjectType = ""; // 科目类型
  String subjectName = ""; // 科目名称
  DateTime? classStartDate;  // 课程开始时间（在初始状态下，如果有返回，则需要显示开课信息）
  CalendarToolVo lsToolVo = CalendarToolVo(); // 连胜道具对象
  CalendarToolVo fhToolVo = CalendarToolVo(); // 复活道具对象
  List<CalendarMonthVo> monthList = []; // 月份列表
  int? guideStatus; // 引导状态；1中断提示 2未正学 3有冰冻 -1(不引导) 4 ：待开始 5：当日未正学 6：当日已正学7：课程已结束

  // 判断某个时间是否超出课程的时间范围
  static bool _isOutLessonTimeRange(DateTime time1, CalendarTimeVo timeVo) {
    return time1.isBefore(timeVo.lessonMinDateTime) || time1.isAfter(timeVo.lessonMaxDateTime);
  }

  static CalendarContentVo getCalendarModelByPageBean(JoJoContinuologyController ctrl,String subjectType,String subjectName,ContinuologyDataModel? bean,String? spineResource,bool firstRequest, {int? guideStatus}) {
    CalendarContentVo calendarVo = CalendarContentVo();
    // 资源下载地址
    calendarVo.spineResource = spineResource ?? "";
    // 科目类型
    calendarVo.subjectType = subjectType;
    // 科目名称（用于埋点）
    calendarVo.subjectName = subjectName; 
    // 科目色
    calendarVo.subjectColor = bean?.subjectColor ?? "#FF9045";
    // 绿豆：思维、英语  叫叫：阅读、小作家、美育, 综合
    calendarVo.isJoJo = ["2", "3", "4", "13"].contains(subjectType);
    // 是否是训练营
    calendarVo.onlyTrainingCamp = bean?.onlyTrainingCamp == 1;

    calendarVo.guideStatus = guideStatus;

    // 处理道具信息
    _dealWithToolsInfo(calendarVo, bean);

    // 处理最大最小时间
    List<CalendarInfo> calendarDayItemList = (bean?.calendar ?? []).toList();
    CalendarTimeVo.setCalendarTimeVoByPageBeanList(ctrl, calendarDayItemList, firstRequest);

    // 组装日历数据
    try {
      DateTime dateTime = ctrl.timeVo.minDateTime; // 用于处于循环

      // 已月为单位，添加学习数据，当时间大于最大时间时跳出循环
      while (dateTime.isAfter(ctrl.timeVo.maxDateTime) == false) {
        CalendarMonthVo monthVo = CalendarMonthVo();
        monthVo.dateTime = dateTime;

        final int totalDays = DateUtils.getDaysInMonth(dateTime.year, dateTime.month); // 当月总天数
        // 组装该月每天的学习数据
        for (int i = 0; i < totalDays; i++) {
          CalendarDayItemVo dayItemVo = _transfromStudyInfoToDayItem(bean, ctrl, calendarDayItemList, calendarVo, dateTime, firstRequest, i);
          // 更新选中的日期数据
          if (ctrl.selectDayItem != null && CalenderUtils.getSameDay(dayItemVo.dateTime, ctrl.selectDayItem!.dateTime)) {
            ctrl.selectDayItem = dayItemVo;
          }
          monthVo.dayItemList.add(dayItemVo);
        }
        calendarVo.monthList.add(monthVo);
        dateTime = DateTime(dateTime.year, dateTime.month + 1, 1);
      }
      // 默认选中日期，只处理一次
      if (firstRequest) {
        // 页码定位到当前月
        ctrl.timeVo.currentPageIndex = calendarVo.monthList.indexWhere((element) {          
          return element.dateTime.isAtSameMomentAs(ctrl.timeVo.currentDateTime);
        });
        // 如果索引不正确，需要纠正
        if (ctrl.timeVo.currentPageIndex < 0) {
          ctrl.timeVo.currentPageIndex = BaseConfig.share.userIsInland() ? 0 : calendarVo.monthList.length - 1;
        }
      }
    } catch (e) {
      l.e("连续学详情页", "日历数据组装异常");
    }

    return calendarVo;
  }

  // 处理道具信息
  static void _dealWithToolsInfo(CalendarContentVo calendarVo,ContinuologyDataModel? bean) {
    List<ItemInfo> propList = (bean?.prop?.items ?? []).toList();
    propList.sort((a, b) => (b.type ?? 0).compareTo((a.type ?? 0)));
    calendarVo.toolsTitle = bean?.prop?.title ?? "";
    calendarVo.lsToolVo = CalendarToolVo.getCalendarToolVoByType(ToolType.lsTool);
    calendarVo.fhToolVo = CalendarToolVo.getCalendarToolVoByType(ToolType.fhTool);
    calendarVo.lsToolVo.jumpRoute = bean?.prop?.jumpRoute ?? "";
    calendarVo.fhToolVo.jumpRoute = bean?.prop?.jumpRoute ?? "";
    for (var element in propList) {
      // 只处理道具数大于0的对象
      if ((element.number ?? 0) == 0) {
        continue;
      }
      if (element.type == ToolType.lsTool) {
        if (element.isFree == 1) {
          calendarVo.lsToolVo.freeIdList.add(element.id ?? 0);
        } else {
          calendarVo.lsToolVo.paidIdList.add(element.id ?? 0);
        }
        calendarVo.lsToolVo.number += (element.number ?? 0);
      }
      if (element.type == ToolType.fhTool) {
        if (element.isFree == 1) {
          calendarVo.fhToolVo.freeIdList.add(element.id ?? 0);
        } else {
          calendarVo.fhToolVo.paidIdList.add(element.id ?? 0);
        }
        calendarVo.fhToolVo.number += (element.number ?? 0);
      }
    }
  }

  // 组装每天的数据
  static CalendarDayItemVo _transfromStudyInfoToDayItem(ContinuologyDataModel? bean, JoJoContinuologyController ctrl, List<CalendarInfo> calendarDayItemList, CalendarContentVo calendarVo, DateTime dateTime, bool firstRequest, int i) {
    DateTime dateNow = DateTime.now();
    CalendarDayItemVo itemtVo = CalendarDayItemVo();
    try {
      itemtVo.pageIndex = calendarVo.monthList.length;
      itemtVo.isUseConcatProp = false;
      itemtVo.dateTime = DateTime(dateTime.year, dateTime.month, i + 1);
      itemtVo.status = _isOutLessonTimeRange(itemtVo.dateTime, ctrl.timeVo) ? CalendarStudyStatus.normal : CalendarStudyStatus.rest;
      _findAndSetCalendarDayItem(bean, calendarVo, ctrl, calendarDayItemList, itemtVo, firstRequest);
      // 确定休息状态是否处于连胜
      if (itemtVo.status.isRest) {
        itemtVo.inWins = calendarVo.inWins;
      }
      // 今日有课，并且首次请求才选中今日(优先级比冰冻叫叫的定位高，会直接覆盖冰冻叫叫的选中数据)
      if (CalenderUtils.getSameDay(itemtVo.dateTime, dateNow) && itemtVo.status.isUnLight && firstRequest) {
        ctrl.selectDayItem = itemtVo;
      }
    } catch (e) {
      l.e("连续学详情页", "月数据异常：${dateTime.toString()}");
    }
    return itemtVo;
  }

  // 根据日期找到对应的日期数据
  static _findAndSetCalendarDayItem(ContinuologyDataModel? bean, CalendarContentVo calendarVo, JoJoContinuologyController ctrl, List<CalendarInfo> calendarDayItemList, CalendarDayItemVo dayItemVo, bool firstRequest) {
    for (var calendarDayItem in calendarDayItemList) {
      if (calendarDayItem.isAdd == true) {
        continue;
      }
      DateTime time = DateTime.fromMillisecondsSinceEpoch(calendarDayItem.time ?? 0);
      if (CalenderUtils.getSameDay(time, dayItemVo.dateTime)) {
        dayItemVo.isUseConcatProp = calendarDayItem.isUseConcatProp == CONCAT_PROP_ENABLED;  // 是否使用过连胜道具
        calendarDayItem.isAdd = true;  // 表记已添加

        // 设置状态
        _setStatus(bean, calendarVo, ctrl, dayItemVo, calendarDayItem, calendarDayItemList, firstRequest);

        // 课程信息
        dayItemVo.classList = calendarDayItem.lessonList ?? [];
        break;
      }
    }
  }

  static void _setStatus(ContinuologyDataModel? bean, CalendarContentVo calendarVo, JoJoContinuologyController ctrl, CalendarDayItemVo dayItemVo, CalendarInfo calendarDayItem, List<CalendarInfo> calendarDayItemList, bool firstRequest) {
  if (calendarDayItem.status == LessonStatus.unLock) {  // 待解锁
    dayItemVo.status = CalendarStudyStatus.lock;
    // 开课时间（等待期时有用）
    if (bean != null && bean.continuous?.status == JoJoContinuologyHeaderDetailDate.statusInit && calendarVo.classStartDate == null) {
      calendarVo.classStartDate = dayItemVo.dateTime;
    }
  } else if (calendarDayItem.status == LessonStatus.unLight) {  // 待点亮
    dayItemVo.status = CalendarStudyStatus.unLight;
    DateTime time = DateTime.fromMillisecondsSinceEpoch(calendarDayItem.time ?? 0);
    DateTime dateNow = DateTime.now();
    if (time.year == dateNow.year && time.month == dateNow.month && firstRequest) {
      if (ctrl.selectDayItem == null) {
        ctrl.selectDayItem = dayItemVo;
      } else if (ctrl.selectDayItem?.status.isFrozen == true) {
        ctrl.selectDayItem = dayItemVo;
      }
    }
  } else if (calendarDayItem.status == LessonStatus.light) {  // 火鸡叫叫
    dayItemVo.status = CalendarStudyStatus.good;
    dayItemVo.inWins = true;
    // 处理连胜状态
    _handleWinningStreak(calendarVo, dayItemVo, calendarDayItem, calendarDayItemList);
  } else if (calendarDayItem.status == LessonStatus.freezed) {  // 冰冻叫叫
    dayItemVo.status = CalendarStudyStatus.freezed;
    // 处理冰冻叫叫
    _handleFrozenJoJo(calendarVo, ctrl, dayItemVo, calendarDayItem, firstRequest);
    // 处理中间休息的连胜状态
    if (calendarDayItem.isUseConcatProp == CONCAT_PROP_ENABLED) {
      _handleWinningStreak(calendarVo, dayItemVo, calendarDayItem, calendarDayItemList);
    }
  } else if (calendarDayItem.status == LessonStatus.dead) {  // 嗝屁叫叫
    dayItemVo.status = CalendarStudyStatus.dead;
    dayItemVo.inWins = calendarDayItem.isUseConcatProp == CONCAT_PROP_ENABLED; 
    // 处理中间休息的连胜状态
    if (calendarDayItem.isUseConcatProp == CONCAT_PROP_ENABLED) {
      _handleWinningStreak(calendarVo, dayItemVo, calendarDayItem, calendarDayItemList);
    }
  }
}

static void _handleWinningStreak(CalendarContentVo calendarVo, CalendarDayItemVo dayItemVo, CalendarInfo calendarDayItem, List<CalendarInfo> calendarDayItemList) {
  // 处理连胜状态，查看下一条数据是否也是火鸡状态，如果是，那么就是处于连胜状态，如果不是，则它就是最后一个火鸡
  calendarVo.inWins = false;
  int itemIndex = calendarDayItemList.indexOf(calendarDayItem);
  if (itemIndex + 1 < calendarDayItemList.length) {
      CalendarInfo nextItem = calendarDayItemList[itemIndex + 1];
      if (nextItem.status == LessonStatus.light || nextItem.isUseConcatProp == CONCAT_PROP_ENABLED) {
        if (dayItemVo.status.isGood) {
          dayItemVo.status = CalendarStudyStatus.finish;
        }
        calendarVo.inWins = true;
      }
      if (nextItem.isUseConcatProp == CONCAT_PROP_ENABLED) {
        calendarVo.inWins = true;
      }
  }
}

static void _handleFrozenJoJo(CalendarContentVo calendarVo, JoJoContinuologyController ctrl, CalendarDayItemVo dayItemVo, CalendarInfo calendarDayItem, bool firstRequest) {
  DateTime time = DateTime.fromMillisecondsSinceEpoch(calendarDayItem.time ?? 0);
  DateTime dateNow = DateTime.now();
  DateTime frostEndTime = DateTime.fromMillisecondsSinceEpoch(calendarDayItem.frostEndTime ?? 0);
  // 处于当月的冰冻叫叫，找到最接近嗝屁的叫叫，用于首次展示的定位逻辑
  if (time.year == dateNow.year && time.month == dateNow.month && firstRequest) {
    if (ctrl.selectDayItem == null || 
      !CalenderUtils.getSameDay(ctrl.selectDayItem!.dateTime, dateNow) && 
      (ctrl.selectDayItem?.freezeJoJoTime == null || frostEndTime.isBefore(ctrl.selectDayItem!.freezeJoJoTime!))) {
      ctrl.selectDayItem = dayItemVo;
      ctrl.selectDayItem!.freezeJoJoTime = frostEndTime;
    }
  }
  dayItemVo.inWins = calendarDayItem.isUseConcatProp == CONCAT_PROP_ENABLED; 
}

  // 获取当天的状态对应的zip内的资源图片
  static File? getDayStatusFile(String spineResource, CalendarDayItemVo itemVo, CalendarStudyStatus? status) {
    CalendarStudyStatus itemStatus = itemVo.status;
    if (status != null) {
      itemStatus = status;
    }
    try {
      String? fileName;
      if (itemStatus.isDead) {
        fileName = "calendar_icon_dead.png";
      } else if (itemStatus.isFrozen) {
        fileName = "calendar_icon_freeze.png";
      } else if (itemStatus.isGood || itemStatus.isFinish || itemVo.showGoodJoJo) {
        fileName = "calendar_icon_dy.png";
      } else if (itemStatus.isUnLight) {
        fileName = "calendar_icon_light.png";
      } else {
        return null;
      }
      return findFilesByExtension(spineResource, fileName);
    } catch (e) {
      return null;
    }
  }

  // 获取选中天对应的zip内的音频资源
  static File? getAudioFile(String spineResource, String fileName) {
    try {
      return findFilesByExtension(spineResource, fileName);
    } catch (e) {
      return null;
    }
  }
}

class CalendarTimeVo {
  int currentPageIndex = 0;  // 当前日历页的索引
  DateTime currentDateTime = DateTime.now(); // 当前页时间
  DateTime minDateTime = DateTime.now(); // 最小时间（日历展示的最小日期）
  DateTime maxDateTime = DateTime.now(); // 最大时间（日历展示的最大日期）
  DateTime lessonMinDateTime = DateTime.now(); // 最小时间（课程的最小日期）
  DateTime lessonMaxDateTime = DateTime.now(); // 最大时间（课程的最大日期）

  static setCalendarTimeVoByPageBeanList(JoJoContinuologyController ctrl, List<CalendarInfo> list, bool firstRequest) {
    DateTime dateNow = DateTime.now();
    DateTime dateOnly = DateTime(DateTime.now().year, DateTime.now().month, dateNow.day);

    if (list.isNotEmpty) {
      // 返回了课程信息
      CalendarInfo firstItem = list.first;
      CalendarInfo lastItem = list.last;
      ctrl.timeVo.lessonMinDateTime = DateTime.fromMillisecondsSinceEpoch(firstItem.time ?? 0);
      ctrl.timeVo.lessonMaxDateTime = DateTime.fromMillisecondsSinceEpoch(lastItem.time ?? 0);
      ctrl.timeVo.minDateTime = DateTime(ctrl.timeVo.lessonMinDateTime.year, ctrl.timeVo.lessonMinDateTime.month, 1);
      int daysInMonth = DateUtils.getDaysInMonth(ctrl.timeVo.lessonMaxDateTime.year, ctrl.timeVo.lessonMaxDateTime.month);
      ctrl.timeVo.maxDateTime = DateTime(ctrl.timeVo.lessonMaxDateTime.year, ctrl.timeVo.lessonMaxDateTime.month, daysInMonth);

      if (!BaseConfig.share.userIsInland() && ctrl.timeVo.lessonMaxDateTime.isBefore(dateOnly)) {
        // 国际版最大时间需要定位到当前月
        ctrl.timeVo.lessonMaxDateTime = dateOnly;
        int daysInMonth = DateUtils.getDaysInMonth(
            ctrl.timeVo.lessonMaxDateTime.year,
            ctrl.timeVo.lessonMaxDateTime.month);
        ctrl.timeVo.maxDateTime =
            DateTime(dateOnly.year, dateOnly.month, daysInMonth);
      }
    } else {
      // 日历信息未正常返回
      ctrl.timeVo.lessonMinDateTime = DateTime(dateNow.year, dateNow.month - 6, 1);
      ctrl.timeVo.lessonMaxDateTime = DateTime(dateNow.year, dateNow.month + 6, 1);
      ctrl.timeVo.minDateTime = DateTime(dateNow.year, dateNow.month - 6, 1);
      ctrl.timeVo.maxDateTime = DateTime(dateNow.year, dateNow.month + 6, 1);
    }
    // 避免再次请求时重置当前月的时间
    if (firstRequest) {
      if (dateNow.isAfter(ctrl.timeVo.minDateTime) && dateNow.isBefore(ctrl.timeVo.maxDateTime)) {
        // 如果存在当前月，使用当前月份的时间
        ctrl.timeVo.currentDateTime = DateTime(dateNow.year, dateNow.month, 1);
      } else {
        if (BaseConfig.share.userIsInland()) {
          // 如果在当前时间之外，需要设置为首月的时间
          ctrl.timeVo.currentDateTime = ctrl.timeVo.minDateTime;
        } else {
          // 如果在当前时间之外，需要设置为当前自然月
          ctrl.timeVo.currentDateTime = dateNow;
        }
      }
    }
  }
}

// 道具信息
class CalendarToolVo {
  List<int> freeIdList = []; // 免费道具列表
  List<int> paidIdList = []; // 收费道具列表
  int type = 0; // 道具类型
  int number = 0; // 道具数目(免费道具+收费道具，优先使用免费道具)
  String name = ''; // 道具名称
  String numZeroAudio = ''; // 道具数目为0时的语音
  String usedAudio = ''; // 已经使用过当前道具的语音
  String jumpRoute = ''; // 点击道具需要跳转的路由

  static CalendarToolVo getCalendarToolVoByType(int type) {
    CalendarToolVo item = CalendarToolVo();
    item.type = type;
    item.number = 0;
    if (type == ToolType.lsTool) {
      item.name = '连胜道具';
    } else if (type == ToolType.fhTool) {
      item.name = '复活道具';
    }
    return item;
  }
}

class CalendarMonthVo {
  DateTime dateTime = DateTime.now(); // 当月所在时间
  List<CalendarDayItemVo> dayItemList = []; // 当月
}

class CalendarDayItemVo {
  int pageIndex = 0;
  CalendarStudyStatus status = CalendarStudyStatus.rest; // 当日学习状态
  DateTime dateTime = DateTime.now(); // 当天所在时间
  DateTime? freezeJoJoTime;  // 冰冻叫叫时间过期时间（用于首次请求定位）
  bool inWins = false; // 是否处于连胜状态
  bool isUseConcatProp = false;  // 当天是否使用了连胜道具
  bool showGoodJoJo = false;  // 是否需要显示火鸡叫叫（只针对该月处于连胜状态的最后一天有效）
  List<LessonInfo> classList = []; // 当天的课程信息（休息则没有对应内容）

  // 当前状态是否是特殊的几种状态之一
  static bool isSpecialStatus(CalendarStudyStatus status) {
    if (!BaseConfig.share.userIsInland()) return false; // 国际化不展示状态图
    return status.isDead || status.isFinish || status.isFrozen || status.isGood || status.isUnLight;
  }
}

enum CalendarStudyStatus {
  normal(name: "普通类型，只显示日期"),
  lock(name: "待解锁"),
  finish(name: "已完成，非火鸡状态"),
  rest(name: "休息"),
  good(name: "得意叫叫，火鸡状态（连胜的最后一天）"),
  unLight(name: "待点亮叫叫"),
  freezed(name: "冻结叫叫"),
  dead(name: "嗝屁叫叫");

  const CalendarStudyStatus({required this.name,});

  final String name;
}

/// 引导状态枚举
enum CalendarGuideStatus {
  /// 中断提示
  interruptPrompt(1),

  /// 未正学
  notStarted(2),

  /// 有冰冻
  hasFrozen(3),

  /// 不引导
  noGuide(-1),

  /// 待开始
  pendingStart(4),

  /// 当日未正学
  todayNotStarted(5),

  /// 当日已正学
  todayStarted(6),

  /// 课程已结束
  courseEnded(7);

  const CalendarGuideStatus(this.value);

  final int value;

  /// 根据值获取对应的枚举
  static CalendarGuideStatus fromValue(int value) {
    for (CalendarGuideStatus status in CalendarGuideStatus.values) {
      if (status.value == value) {
        return status;
      }
    }
    throw ArgumentError('Invalid GuideStatus value: $value');
  }
}

extension CalendarStudyStatusExtension on CalendarStudyStatus {
  bool get isNormal => this == CalendarStudyStatus.normal;
  bool get isLock => this == CalendarStudyStatus.lock;
  bool get isFinish => this == CalendarStudyStatus.finish;
  bool get isRest => this == CalendarStudyStatus.rest;
  bool get isGood => this == CalendarStudyStatus.good;
  bool get isUnLight => this == CalendarStudyStatus.unLight;
  bool get isFrozen => this == CalendarStudyStatus.freezed;
  bool get isDead => this == CalendarStudyStatus.dead;
}

class ConfigSize {
  static int get daysInWeek => 7;  // 一周有7天
  static double get dateItemWidth => 40.rdp; // 日期宽度
  static double get dateItemHeight => 40.rdp; // 日期高度
  static double get weekTitleHeight => 22.rdp; // 周标题栏高度
  static double get contentSpace => 20.rdp; // 主体内容与屏幕左右的间距
  static double get itemSpace => 2.rdp; // 日期上下的间距
  static double get calendarBorderRadius => 20.rdp;  // 日历边框圆角
  static double get calendarPadingTop => 14.rdp;  // 日历内容距离顶部的距离
  static double get dateBgColorHeight => 38.rdp;  // 连胜背景高度
  static double get studyBtnWidth => 112.rdp;  // 学习内容按钮宽度
  static double get studyBtnHeight => 30.rdp;  // 学习内容按钮宽度
  static double get classInfoIconWidth => 40.rdp;  // 课程信息图标宽度
  static double get classInfoIconHeight => 40.rdp; // 课程信息图标高度
  static double get classInfoTopSpace => 14.rdp;  // 课程信息顶部间距
  static double get classInfoBottomSpace => 14.rdp;  // 课程信息底部间距
  static double get timeSwitchHeight => 50.rdp;  // 日期选择内容高度
  static double get headerSpineWidth => 128.rdp; // 头部动画的宽度
  static double get headerSpineHeight => 128.rdp; // 头部动画的高度
  static double get headerSpineResourceWidth => 256.rdp; // 和动效老师约定好的资源宽度
  static double get headerSpineResourceFireWidth => 256.rdp; // 和动效老师约定好的火焰类资源宽度
}

class LessonStatus {
  static const int unLock = -1; // 待解锁
  static const int unLight = 0;  // 待点亮
  static const int light = 1;  // 已点亮
  static const int freezed = 2;  // 冰冻
  static const int dead = 3;  // 嗝屁
}

class ToolType {
  static const int lsTool = 2002;  // 连胜道具 
  static const int fhTool = 2001;  // 复活道具
}
