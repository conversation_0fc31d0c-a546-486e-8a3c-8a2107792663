// This is a basic Flutter widget test.
//
// To perform an interaction with a widget in your test, use the WidgetTester
// utility in the flutter_test package. For example, you can send tap and scroll
// gestures. You can also use WidgetTester to find child widgets in the widget
// tree, read text, and verify that the values of widget properties are correct.

import 'package:flutter/cupertino.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/download/jojo_down_gray.dart';
import 'package:jojo_flutter_base/models/exception_data.dart';
import 'package:jojo_flutter_base/widgets/common/page_loading_v25.dart';
import 'package:jojo_flutter_plan_pkg/common/bridge/mock_brige_calendar.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_activity_detail_page/controller.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_activity_detail_page/model/activity_data.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_activity_detail_page/model/activity_detail_data.dart';
import 'package:jojo_flutter_plan_pkg/service/plan_activity_detail_api.dart';
import 'package:mockito/mockito.dart';

class MockDownManager extends Mock implements AbsDownloadManager {
  @override
  Future<void> downloadUrl(List<String> urlList, {Function(double p1)? progressListener, Function(Map<String, String> p1)? successListener, Function(UnifiedExceptionData p1)? failListener, bool isNeedCancel = true}) {
    successListener?.call(
        {"url": "https://jojopublicfat.jojoread.com/vulcan/nuwa-admin/appicon/810171081143781379/1749799847003d45xdb.png?checksumV2=md5Hex%3D880b37ad6dc1c5f546b30cff90b63610", "path": "11.png"});
    return Future.value();
  }
}

/// Mock 下载管理器 - 可以控制成功/失败
class MockDownloadManager extends Mock implements AbsDownloadManager {
    final bool shouldSucceed;
    final bool shouldUnzip;
    final String? errorMessage;

    MockDownloadManager({
        this.shouldSucceed = true,
        this.shouldUnzip = false,
        this.errorMessage,
    });

    @override
    Future<void> downloadUrl(
        List<String> urlList, {
            Function(double)? progressListener,
            Function(Map<String, String>)? successListener,
            Function(UnifiedExceptionData)? failListener,
            bool isNeedCancel = true,
        }) async {

        if (shouldSucceed) {
            // 模拟下载成功
            Map<String, String> result = {};
            for (String url in urlList) {
                String fileName = url.split('/').last;
                if (shouldUnzip && fileName.endsWith('.zip')) {
                    // 模拟解压后的路径
                    result[url] = "/mock/unzipped/path/$fileName";
                } else {
                    // 模拟普通文件路径
                    result[url] = "/mock/download/path/$fileName";
                }
            }
            successListener?.call(result);
        } else {
            // 模拟下载失败
            failListener?.call(UnifiedExceptionData(
                message: errorMessage ?? "下载失败",
                code: 1000
            ));
        }
    }
}

// 创建 Dio 的 Mock 类
class MockActivityDetailPageApiService extends Mock implements ActivityDetailApi {
  final PlanActivityData? activityData;
  final bool shouldThrowException;

  MockActivityDetailPageApiService(this.activityData, {this.shouldThrowException = false});

  @override
  Future<PlanActivityData> getCourseSegmentsInfo({required int activityId, required int classId, required int courseId, required int pageId}) {
    if (shouldThrowException) {
      throw Exception("网络请求失败");
    }
    return Future.value(MockActivityDetailPageApiService.mockPageData());
  }

  static PlanActivityData? mockPageData() {
    Map<String, dynamic> jsonDate = {
      "themeCard":{
        "progressHeadColor" :"#f5a623",
         "progressTailColor":"#8b572a",
          "backgroundRes":"https://jojopublicfat.jojoread.com/edu/admin/teacher/812028632118493187/1750242717738/bg_medal.zip.flutter"
      },
      "activityId": 0,
      "activitySubject":"活动名称",
      "tasks": [
          {
              "taskId": 7866,
              "name": "主题创作",
              "taskType": "STEPWISE",
              "isFinish": 0,
              "isGet": 0,
              "conditions": [
                  {
                      "currentValue": 2,
                      "targetValue": 2,
                      "type": 0,
                      "lessonIds": []
                  }
              ],
              "rewards": [
                  {
                      "rewardId": 87,
                      "type": 5,
                      "typeValue": "",
                      "lockImage": "",
                      "unlockImage": "",
                      "isGet": 0,
                      "isPopup": 0,
                      "resourceIos": "",
                      "resourceAndroid": "",
                      "resourceFlutter": "",
                      "bizId": "",
                      "rewardBizUrl": ""
                  }
              ],
              "taskExtendResource": {
                  "rewardDisplayUrl": "https://jojopublicfat.jojoread.com/edu/admin/teacher/812029304045993987/1750242875460/M_achievements.zip.flutter",
                  "mainText": "去外地去外地",
                  "subText": "卫栖梧萨顶顶"
              }
          },
          {
              "taskId": 7866,
              "name": "主题创作",
              "taskType": "STEPWISE",
              "isFinish": 0,
              "isGet": 0,
              "conditions": [
                  {
                      "currentValue": 6,
                      "targetValue": 6,
                      "type": 0,
                      "lessonIds": []
                  }
              ],
              "rewards": [
                  {
                      "rewardId": 87,
                      "type": 11,
                      "typeValue": "",
                      "lockImage": "",
                      "unlockImage": "",
                      "isGet": 0,
                      "isPopup": 0,
                      "resourceIos": "",
                      "resourceAndroid": "",
                      "resourceFlutter": "",
                      "bizId": "",
                      "rewardBizUrl": ""
                  }
              ],
              "taskExtendResource": {
                  "rewardDisplayUrl": "https://jojopublicfat.jojoread.com/edu/admin/teacher/812029388053710859/1750242901110/M_raffle_ticket.zip.flutter",
                  "mainText": "",
                  "subText": ""
              }
          },
          {
              "taskId": 7866,
              "name": "主题创作",
              "taskType": "STEPWISE",
              "isFinish": 0,
              "isGet": 0,
              "conditions": [
                  {
                      "currentValue": 9,
                      "targetValue": 9,
                      "type": 0,
                      "lessonIds": []
                  }
              ],
              "rewards": [
                  {
                      "rewardId": 87,
                      "type": 11,
                      "typeValue": "",
                      "lockImage": "",
                      "unlockImage": "",
                      "isGet": 0,
                      "isPopup": 0,
                      "resourceIos": "",
                      "resourceAndroid": "",
                      "resourceFlutter": "",
                      "bizId": "",
                      "rewardBizUrl": ""
                  }
              ],
              "taskExtendResource": {
                  "rewardDisplayUrl": "https://jojopublicfat.jojoread.com/edu/admin/teacher/812029480458420225/1750242923080/M_achievements.zip.flutter",
                  "mainText": "",
                  "subText": ""
              }
          },
          {
              "taskId": 7866,
              "name": "主题创作",
              "taskType": "STEPWISE",
              "isFinish": 0,
              "isGet": 0,
              "conditions": [
                  {
                      "currentValue": 12,
                      "targetValue": 18,
                      "type": 0,
                      "lessonIds": []
                  }
              ],
              "rewards": [
                  {
                      "rewardId": 87,
                      "type": 11,
                      "typeValue": "",
                      "lockImage": "",
                      "unlockImage": "",
                      "isGet": 0,
                      "isPopup": 0,
                      "resourceIos": "",
                      "resourceAndroid": "",
                      "resourceFlutter": "",
                      "bizId": "",
                      "rewardBizUrl": ""
                  }
              ],
              "taskExtendResource": {
                  "rewardDisplayUrl": "https://jojopublicfat.jojoread.com/edu/admin/teacher/812029388053710859/1750242901110/M_raffle_ticket.zip.flutter",
                  "mainText": "",
                  "subText": ""
              }
          },
          {
              "taskId": 7866,
              "name": "主题创作",
              "taskType": "STEPWISE",
              "isFinish": 0,
              "isGet": 0,
              "conditions": [
                  {
                      "currentValue": 12,
                      "targetValue": 22,
                      "type": 0,
                      "lessonIds": []
                  }
              ],
              "rewards": [
                  {
                      "rewardId": 87,
                      "type": 11,
                      "typeValue": "",
                      "lockImage": "",
                      "unlockImage": "",
                      "isGet": 0,
                      "isPopup": 0,
                      "resourceIos": "",
                      "resourceAndroid": "",
                      "resourceFlutter": "",
                      "bizId": "",
                      "rewardBizUrl": ""
                  }
              ],
              "taskExtendResource": {
                  "rewardDisplayUrl": "https://jojopublicfat.jojoread.com/edu/admin/teacher/812029304045993987/1750242875460/M_achievements.zip.flutter",
                  "mainText": "",
                  "subText": ""
              }
          },
          {
              "taskId": 7866,
              "name": "主题创作",
              "taskType": "STEPWISE",
              "isFinish": 0,
              "isGet": 0,
              "conditions": [
                  {
                      "currentValue": 12,
                      "targetValue": 25,
                      "type": 0,
                      "lessonIds": []
                  }
              ],
              "rewards": [
                  {
                      "rewardId": 87,
                      "type": 11,
                      "typeValue": "",
                      "lockImage": "",
                      "unlockImage": "",
                      "isGet": 0,
                      "isPopup": 0,
                      "resourceIos": "",
                      "resourceAndroid": "",
                      "resourceFlutter": "",
                      "bizId": "",
                      "rewardBizUrl": ""
                  }
              ],
              "taskExtendResource": {
                  "rewardDisplayUrl": "https://jojopublicfat.jojoread.com/edu/admin/teacher/812029388053710859/1750242901110/M_raffle_ticket.zip.flutter",
                  "mainText": "",
                  "subText": ""
              }
          },
          {
              "taskId": 7866,
              "name": "主题创作",
              "taskType": "STEPWISE",
              "isFinish": 0,
              "isGet": 0,
              "conditions": [
                  {
                      "currentValue": 6,
                      "targetValue": 30,
                      "type": 0,
                      "lessonIds": []
                  }
              ],
              "rewards": [
                  {
                      "rewardId": 87,
                      "type": 12,
                      "typeValue": "",
                      "lockImage": "",
                      "unlockImage": "",
                      "isGet": 0,
                      "isPopup": 0,
                      "resourceIos": "",
                      "resourceAndroid": "",
                      "resourceFlutter": "",
                      "bizId": "",
                      "rewardBizUrl": ""
                  }
              ],
              "taskExtendResource": {
                  "rewardDisplayUrl": "https://jojopublicfat.jojoread.com/edu/admin/teacher/812029598033149953/1750242945408/Physical_doll.zip.flutter",
                  "mainText": "",
                  "subText": ""
              }
          }
      ],
      "freePages": [
          {
              "id": 0,
              "pageType": "",
              "components": [
                  {
                      "orderNum": 0,
                      "componentType": "IMG",
                      "imgUrl":"https://jojopublicfat.jojoread.com/vulcan/nuwa-admin/appicon/815222121685550081/imgc1.png?checksumV2=md5Hex%3Da2f2d522e07636875fa775ae525711fa",
                       "videoUrl": null,
                      "videoCoverImg": null,
                       "videoBgImg": null,
                       "topImg": null,
                        "surroundImg": null,
                        "bottomImg": null
                  
                  },
                  {
                      "orderNum": 0,
                      "componentType": "TASK_PROGRESS",
                      "imgUrl":null,
                       "videoUrl": null,
                      "videoCoverImg": null,
                       "videoBgImg": null,
                       "topImg": "https://jojopublicfat.jojoread.com/vulcan/nuwa-admin/appicon/815221943763174401/bgup.png?checksumV2=md5Hex%3D77b9bcd3fcb13a86681668f661fb1a52",
                        "surroundImg": "https://jojopublicfat.jojoread.com/vulcan/nuwa-admin/appicon/815222401479181313/taskbgm.png?checksumV2=md5Hex%3D47a4b403f29c4b517a7e01bd21e0f8a3",
                        "bottomImg": "https://jojopublicfat.jojoread.com/vulcan/nuwa-admin/appicon/815222316833932289/taskbgdown.png?checksumV2=md5Hex%3Db1ddbe8b7182d10eaed9523865f0779c"
                  
                  },
                  {
                      "orderNum": 0,
                      "componentType": "IMG",
                      "imgUrl":"https://jojopublicfat.jojoread.com/vulcan/nuwa-admin/appicon/815222243366503425/imgc4.png?checksumV2=md5Hex%3D215b0c05c5c7519a3e828256b6fe1130",
                       "videoUrl": null,
                      "videoCoverImg": null,
                       "videoBgImg": null,
                       "topImg": null,
                        "surroundImg": null,
                        "bottomImg": null
                  
                  },
                  {
                      "orderNum": 0,
                      "componentType": "VIDEO",
                      "imgUrl":null,
                       "videoUrl": null,
                      "videoCoverImg": "https://jojopublicfat.jojoread.com/vulcan/nuwa-admin/appicon/815222534535087105/videoCover.png?checksumV2=md5Hex%3Db1d9c9c9b138d899cbc33eec880498c7",
                       "videoBgImg": "https://jojopublicfat.jojoread.com/vulcan/nuwa-admin/appicon/815222473445049345/videobg.png?checksumV2=md5Hex%3D07390736860cd9beb28a4a9fb8807b97",
                       "topImg": null,
                        "surroundImg": null,
                        "bottomImg": null
                  
                  }
              ]
          }
      ]
  }
;
    PlanActivityData test = PlanActivityData.fromJson(jsonDate);
    return test;
  }
}

void main() {
  late PlanActivityDetailCtrl controller;
  late MockActivityDetailPageApiService mockApiService;
  setUp(() {

    mockApiService =
        MockActivityDetailPageApiService(MockActivityDetailPageApiService.mockPageData());
    JoJoNativeBridge.registerMocker(JoJoBridgeCommonMockerCalendar());
    controller = PlanActivityDetailCtrl(
        api: mockApiService,
        courseId: 1333,
        pageId: 122,
        activityId: 111,
        subjectColor: null,
        classId: 111,
        buriedString: null,
    );
  });
  
  group('activity_controller tests:', () {
      WidgetsFlutterBinding.ensureInitialized();

    test('getDetailInfoData test', () async {
      await controller.getDetailInfoData();
    });

    test('refreshPage test', () {
        controller.refreshPage();
        expect(controller.state.pageStatus == PageStatus.error , false);
    });

    test('showErrorPage test', () async {
        controller.showErrorPage(Exception("test"), "测试代码");
        expect(controller.state.pageStatus == PageStatus.error , true);
    });

    test('onAllResourcesDownloaded test', () async {
        PlanActivityData data = await mockApiService.getCourseSegmentsInfo(activityId: 111, classId: 222, courseId: 111, pageId: 11);
        PlanActivityDetailData? detailData = PlanActivityDetailData.getDataModel(data, 0);
        controller.onAllResourcesDownloaded(detailData!);
        expect(controller.state.pageStatus == PageStatus.success , true);
    });

      test('onAllResourcesDownloaded test', () async {
          PlanActivityData data = await mockApiService.getCourseSegmentsInfo(activityId: 111, classId: 222, courseId: 111, pageId: 11);
          PlanActivityDetailData? detailData = PlanActivityDetailData.getDataModel(data, 0);
          controller.downloadResource(data, null);
          expect(controller.state.pageStatus == PageStatus.error , true);
          controller.downloadResource(null, detailData);
          expect(controller.state.pageStatus == PageStatus.error , true);
          bool result1 = controller.isEssentialAnimationResourcesDownloaded(detailData!, true);
          expect(result1 == false , true);
          bool result2 = controller.isEssentialPicResourcesDownloaded(detailData, true);
          controller.jumpToMyGiftPage();
          expect(result2 == false , true);
      });

      test('dealWithImageData test', () async {
          PlanActivityData data = await mockApiService.getCourseSegmentsInfo(activityId: 111, classId: 222, courseId: 111, pageId: 11);
          PlanActivityDetailData? detailData = PlanActivityDetailData.getDataModel(data, 0);
          String url = "https://jojopublicfat.jojoread.com/vulcan/nuwa-admin/appicon/815222473445049345/videobg.png?checksumV2=md5Hex%3D07390736860cd9beb28a4a9fb8807b97";
          controller.dealWithImageData({"url":url,"path":"xxx"} ,detailData!, true);
          controller.dealWithAnimationData({"url":"https://jojopublicfat.jojoread.com/edu/admin/teacher/812029388053710859/1750242901110/M_raffle_ticket.zip.flutter","path":"xxx"}, detailData, true);
          controller.dealWithAnimationData({"url":"https://jojopublicfat.jojoread.com/edu/admin/teacher/812028632118493187/1750242717738/bg_medal.zip.flutter","path":"xxx"}, detailData, true);
          PlanActivityDetailDataPicVo picVo = PlanActivityDetailDataPicVo();
          picVo.url = url;
          controller.updateLocalPath(picVo, {url:url});
      });

      test('requestData test', () async {
          controller = PlanActivityDetailCtrl(
              api: mockApiService,
              courseId: 0,
              pageId: 0,
              activityId: 0,
              subjectColor: null,
              classId: 0, 
              buriedString: null,
          );
          controller.callBackFailedListener("logTxt", () => {});
          PlanActivityData? data = await controller.requestData();
          expect(data == null , true);
      });

      test('getDetailInfoData exception test - requestData throws exception', () async {
          // 创建一个会抛出异常的 mock API 服务
          MockActivityDetailPageApiService exceptionApiService =
              MockActivityDetailPageApiService(null, shouldThrowException: true);

          // 创建新的 controller 使用会抛异常的 API
          PlanActivityDetailCtrl exceptionController = PlanActivityDetailCtrl(
              api: exceptionApiService,
              courseId: 1333,
              pageId: 122,
              activityId: 111,
              subjectColor: null,
              classId: 111,
              buriedString: null,
          );

          // 验证初始状态不是错误状态
          expect(exceptionController.state.pageStatus == PageStatus.error, false);

          // 调用 getDetailInfoData，应该会捕获异常并调用 showErrorPage
          await exceptionController.getDetailInfoData();

          // 验证状态变为错误状态
          expect(exceptionController.state.pageStatus == PageStatus.error, true);
          expect(exceptionController.state.exception != null, true);
          expect(exceptionController.state.exception.toString().contains("接口请求失败"), true);
      });
  });
}
